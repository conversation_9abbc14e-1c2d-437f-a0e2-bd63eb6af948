from pathlib import Path
from dotenv import load_dotenv

# Load .env.production first, then fallback to .env
env_prod_path = Path(__file__).resolve().parent / ".env.production"
env_dev_path = Path(__file__).resolve().parent / ".env"

if env_prod_path.exists():
    load_dotenv(dotenv_path=env_prod_path)
else:
    load_dotenv(dotenv_path=env_dev_path)


try:
    import gspread
except ImportError:
    gspread = None
"""
AlphaPredatorBot FastAPI Server
Handles API endpoints for news ingestion, token discovery, AI signals,
analytics, and trade logs.
"""

# --- Standard Library ---
import os
import json
import csv
from pathlib import Path

# --- Third-party Packages ---
from fastapi import FastAPI, status, Request, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from typing import List, Dict

from datetime import datetime
import asyncio
import time

from google.oauth2 import id_token

# Import dashboard preloader (commented out for now)
# from dashboard_preloader import dashboard_preloader

# Import CoinGecko integration
from coingecko_integration import coingecko_integration

from config import ALLOWED_EMAILS
from auth import create_access_token, get_current_user


# --- Logging ---
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("alpha_api")

# --- FastAPI App Instance ---
app = FastAPI()


# Get allowed origins from environment or use defaults
def get_allowed_origins():
    env_origins = os.getenv("ALLOWED_ORIGINS", "")
    if env_origins:
        return [origin.strip() for origin in env_origins.split(",")]

    # Default origins for development and production
    return [
        "http://localhost",  # Docker frontend on port 80
        "http://localhost:80",  # Explicit port 80
        "http://localhost:3000",
        "http://localhost:5173",  # Vite dev server
        "https://alphafrontend.app.runonflux.io",
        "https://www.alphapredatorbot.xyz",
        "https://alphafrontend_37466.app.runonflux.io",
        "*",  # Allow all origins for now to debug
    ]


# Add CORS middleware to allow requests from frontend origins
allowed_origins = get_allowed_origins()
logger.info(f"CORS allowed origins: {allowed_origins}")

app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
    expose_headers=["*"],
)

# --- Internal Modules ---
from error_codes import error_response

from news_ingestion.cointelegraph import fetch_cointelegraph_articles
from token_selector import (
    get_spike_tokens,
    get_newly_listed_tokens,
    sourceCredibilityMap,
    generate_top_token_list,
)

# SDK-based imports for better performance
from kucoin_sdk_migration import kucoin_sdk
from coingecko_sdk_migration import coingecko_sdk
from ai_clients_sdk_migration import get_ai_clients_status

from enhanced_token_selector import get_enhanced_discover_tokens
from strategy_orchestrator import (
    get_optimal_trade_decision,
    get_strategy_performance_summary,
)
from advanced_data_collector import collect_200_data_points
from smart_tokenmetrics_client import (
    get_optimized_trading_data,
    get_token_analysis,
    get_cost_report,
)
from tokenmetrics_usage_monitor import get_usage_summary
from coingecko_integration import coingecko_integration
from analytics_engine import generate_analytics
from routes.live_trades import router as live_trades_router  # Live trades
from manual_trading_api import manual_trading_bp
from optimized_api_endpoints import router as optimized_router

app.include_router(live_trades_router)
app.include_router(optimized_router)


# Add manual trading routes
@app.post("/api/manual-trading/execute-command")
async def execute_manual_trading_command(request: Request):
    """Execute manual trading command"""
    try:
        data = await request.json()
        command = data.get("command", "").strip()

        if not command:
            return JSONResponse(
                {"success": False, "error": "No command provided"}, status_code=400
            )

        # Import and use the trading engine
        from manual_trading_api import trading_engine

        # Parse the command
        parsed_command = trading_engine.parse_trading_command(command)

        if parsed_command["action"] == "unknown":
            return JSONResponse(
                {
                    "success": False,
                    "error": "Could not understand command",
                    "parsed_command": parsed_command,
                    "suggestions": [
                        "buy 5 usdt of btc",
                        "sell all eth",
                        "get balance",
                        "btc price",
                    ],
                },
                status_code=400,
            )

        # Execute the command
        result = await trading_engine.execute_command(parsed_command)

        return JSONResponse(
            {
                "success": result.get("success", False),
                "result": result,
                "parsed_command": parsed_command,
                "original_command": command,
            }
        )

    except Exception as e:
        logger.error(f"Manual trading error: {e}")
        return JSONResponse({"success": False, "error": str(e)}, status_code=500)


@app.post("/api/manual-trading/parse-command")
async def parse_manual_trading_command(request: Request):
    """Parse a trading command without executing it"""
    try:
        data = await request.json()
        command = data.get("command", "").strip()

        if not command:
            return JSONResponse(
                {"success": False, "error": "No command provided"}, status_code=400
            )

        from manual_trading_api import trading_engine

        parsed_command = trading_engine.parse_trading_command(command)

        return JSONResponse(
            {
                "success": True,
                "parsed_command": parsed_command,
                "original_command": command,
            }
        )

    except Exception as e:
        logger.error(f"Command parsing error: {e}")
        return JSONResponse({"success": False, "error": str(e)}, status_code=500)


# from ai_signals import run_ai_signal_decision
# from routes.ai_decisions import router as ai_decision_router
from routes.trades_summary import router as trade_summary_router
from token_discovery import get_discover_tokens
from sentiment_engine import get_sentiment_feed  # Import the sentiment feed function
from ai_logic_loader import load_ai_logic
from trade_logger import load_trade_summary, load_live_trades
from analytics_engine import get_analytics_summary
from pnl_dashboard import get_pnl_summary  # Added import for get_pnl_summary
from micro_bot import start_micro_bot, stop_micro_bot, get_micro_bot_status
from arbitrage_finder import find_arbitrage_opportunities
from enhanced_arbitrage import (
    get_enhanced_arbitrage_opportunities,
    execute_enhanced_arbitrage_trade,
    get_arbitrage_performance_stats,
)
from live_runner import start_alpha_bot, stop_alpha_bot, get_alpha_bot_status
from tokenmetrics_api import TokenMetricsAPI
from tokenmetrics_moonshots import get_moonshot_tokens, analyze_token_moonshot_potential

from cache import get_cached_data, set_cached_data

# Initialize TokenMetrics API
tokenmetrics_api = TokenMetricsAPI()

# ————————————————————
# NEWS ENDPOINTS WITH LIVE DATA
# ————————————————————


@app.get("/api/news/token/{symbol}", tags=["News"])
async def get_token_news(symbol: str, current_user: str = Depends(get_current_user)):
    """Get comprehensive news data for a specific token including Reddit/GitHub"""
    try:
        logger.info(f"Fetching comprehensive news for token: {symbol}")

        # Import news functions
        from reddit_github_alpha import (
            fetch_reddit_signals_for_token,
            fetch_github_signals,  # Use the correct function if fetch_github_signals_for_token does not exist
        )
        from sentiment_engine import get_combined_sentiment

        # Get token-specific data from multiple sources
        reddit_signals = fetch_reddit_signals_for_token(symbol)
        github_signals = (
            fetch_github_signals()
        )  # fetch_github_signals expects no arguments
        sentiment_data = get_combined_sentiment(symbol)

        # Combine all news sources
        all_news = []

        # Add Reddit signals
        for signal in reddit_signals:
            all_news.append(
                {
                    "source": "reddit",
                    "title": signal.get("title", ""),
                    "url": signal.get("url", ""),
                    "created_at": signal.get("created_at", ""),
                    "credibility": signal.get("credibility", 0.5),
                    "relevance_score": signal.get("relevance_score", 0.0),
                    "type": "social",
                }
            )

        # Add GitHub signals
        for signal in github_signals:
            all_news.append(
                {
                    "source": "github",
                    "title": signal.get("title", ""),
                    "description": signal.get("description", ""),
                    "url": signal.get("url", ""),
                    "created_at": signal.get("created_at", ""),
                    "credibility": signal.get("credibility", 0.5),
                    "relevance_score": signal.get("relevance_score", 0.0),
                    "type": "development",
                }
            )

        # Sort by relevance and recency
        all_news.sort(
            key=lambda x: (x.get("relevance_score", 0), x.get("created_at", "")),
            reverse=True,
        )

        response_data = {
            "symbol": symbol.upper(),
            "news_count": len(all_news),
            "reddit_signals": len(reddit_signals),
            "github_signals": len(github_signals),
            "sentiment": sentiment_data,
            "news": all_news[:20],  # Return top 20 most relevant
            "timestamp": datetime.now().isoformat(),
        }

        logger.info(f"Successfully fetched {len(all_news)} news items for {symbol}")
        return JSONResponse(status_code=200, content=response_data)

    except Exception as e:
        logger.error(f"Error fetching news for {symbol}: {e}", exc_info=True)
        return JSONResponse(
            status_code=500,
            content={"error": f"Failed to fetch news for {symbol}: {str(e)}"},
        )


@app.get("/api/news/live", tags=["News"])
async def get_live_news(limit: int = 50, current_user: str = Depends(get_current_user)):
    """Get live trading-focused news from relevant sources"""
    try:
        logger.info("Fetching live trading news from relevant sources")

        # Import AI optimization tracking
        from ai_api_optimizer import record_endpoint_call
        import time

        start_time = time.time()

        from reddit_github_alpha import fetch_reddit_signals, fetch_rss_signals
        from discord_news_bot import get_latest_discord_news
        from trading_news_filter import filter_for_trading_news, get_trading_news_stats

        # Get news from all trading-relevant sources
        reddit_news = fetch_reddit_signals()[:20]  # Reddit crypto discussions

        # Get professional crypto news from RSS feeds (CoinDesk, CoinTelegraph, etc.)
        rss_news = fetch_rss_signals()[:30]  # Professional crypto news

        # Try to get Discord news (usually trading-focused)
        try:
            discord_news = get_latest_discord_news(limit=20)
        except:
            discord_news = []

        # Combine and format all news sources
        all_news = []

        for news in reddit_news:
            all_news.append({**news, "type": "social", "platform": "reddit"})

        for news in rss_news:
            all_news.append({**news, "type": "professional", "platform": "rss"})

        for news in discord_news:
            all_news.append({**news, "type": "news", "platform": "discord"})

        # Filter for trading relevance (removes GitHub and non-trading content)
        trading_news = filter_for_trading_news(all_news, min_relevance=0.3)

        # Sort by trading relevance and recency
        trading_news.sort(
            key=lambda x: (
                x.get("trading_relevance", 0),
                x.get("credibility", 0),
                x.get("created_at", ""),
            ),
            reverse=True,
        )

        # Get trading news statistics
        trading_stats = get_trading_news_stats(trading_news)

        response_data = {
            "total_raw_news": len(all_news),
            "filtered_trading_news": len(trading_news),
            "reddit_count": len(reddit_news),
            "rss_count": len(rss_news),
            "discord_count": len(discord_news),
            "avg_trading_relevance": trading_stats.get("avg_relevance", 0),
            "recent_news_count": trading_stats.get("recent_count", 0),
            "news": trading_news[:limit],  # Return filtered trading news
            "timestamp": datetime.now().isoformat(),
            "filter_applied": "trading_relevance >= 0.3",
            "sources": [
                "Reddit",
                "CoinDesk",
                "CoinTelegraph",
                "Decrypt",
                "CryptoNews",
                "Discord",
            ],
        }

        # Record API call for AI optimization
        response_time = time.time() - start_time
        data_changed = len(trading_news) > 0  # Data changed if we got news
        record_endpoint_call(
            "news/live", response_time=response_time, data_changed=data_changed
        )

        logger.info(f"Successfully fetched {len(all_news)} live news items")
        return JSONResponse(status_code=200, content=response_data)

    except Exception as e:
        logger.error(f"Error fetching live news: {e}", exc_info=True)
        return JSONResponse(
            status_code=500, content={"error": f"Failed to fetch live news: {str(e)}"}
        )


# ————————————————————
# COST MONITORING ENDPOINTS
# ————————————————————


@app.get("/api/cost-monitoring", tags=["Cost Monitoring"])
async def get_cost_monitoring(current_user: str = Depends(get_current_user)):
    """Get real-time cost monitoring data"""
    try:
        logger.info("Fetching real-time cost monitoring data")

        # Import real-time cost monitoring system
        from real_time_cost_monitor import get_real_time_cost_data
        from ai_api_optimizer import record_endpoint_call, get_optimal_interval
        import time

        # Record this API call for optimization
        start_time = time.time()

        # Get actual real-time usage and cost data
        cost_data = get_real_time_cost_data()

        # Record the call for AI optimization
        response_time = time.time() - start_time
        record_endpoint_call(
            "cost-monitoring", response_time=response_time, data_changed=True
        )

        logger.info("Successfully generated cost monitoring data")
        return JSONResponse(status_code=200, content=cost_data)

    except Exception as e:
        logger.error(f"Error fetching cost monitoring data: {e}", exc_info=True)
        return JSONResponse(
            status_code=500,
            content={"error": f"Failed to fetch cost monitoring data: {str(e)}"},
        )


# ————————————————————
# AI API OPTIMIZATION ENDPOINTS
# ————————————————————


@app.get("/api/ai-optimization", tags=["AI Optimization"])
async def get_ai_optimization_status(current_user: str = Depends(get_current_user)):
    """Get AI API optimization status and current intervals"""
    try:
        logger.info("Fetching AI optimization status")

        from ai_api_optimizer import get_optimization_status, run_ai_optimization

        # Get current status
        status = get_optimization_status()

        # Add recommended intervals for frontend
        status["frontend_intervals"] = {
            "cost_monitoring_refresh": status["current_intervals"].get(
                "cost-monitoring", 45
            )
            * 1000,  # Convert to ms
            "news_refresh": status["current_intervals"].get("news/live", 60) * 1000,
            "tokens_refresh": status["current_intervals"].get("tokens", 90) * 1000,
            "discover_refresh": status["current_intervals"].get("discover", 120) * 1000,
            "analytics_refresh": status["current_intervals"].get("analytics", 300)
            * 1000,
            "pnl_refresh": status["current_intervals"].get("pnl", 30) * 1000,
            "trades_refresh": status["current_intervals"].get("trades/live", 15) * 1000,
            "portfolio_refresh": status["current_intervals"].get("portfolio", 20)
            * 1000,
        }

        return JSONResponse(status_code=200, content=status)

    except Exception as e:
        logger.error(f"Error fetching AI optimization status: {e}", exc_info=True)
        return JSONResponse(
            status_code=500,
            content={"error": f"Failed to fetch AI optimization status: {str(e)}"},
        )


@app.post("/api/ai-optimization/run", tags=["AI Optimization"])
async def run_ai_optimization_cycle(current_user: str = Depends(get_current_user)):
    """Run AI optimization cycle to analyze and adjust API intervals"""
    try:
        logger.info("Running AI optimization cycle")

        from ai_api_optimizer import run_ai_optimization

        results = run_ai_optimization()

        return JSONResponse(status_code=200, content=results)

    except Exception as e:
        logger.error(f"Error running AI optimization: {e}", exc_info=True)
        return JSONResponse(
            status_code=500,
            content={"error": f"Failed to run AI optimization: {str(e)}"},
        )


# ————————————————————
# ORIGINAL NEWS ENDPOINTS
# ————————————————————


@app.post("/api/news/fetch", tags=["News"])
def fetch_and_save_news(current_user: str = Depends(get_current_user)):
    """Fetch news from Cointelegraph and save to file."""
    file_path = "data/news.json"
    news: List[Dict] = []
    try:
        news: List[Dict] = fetch_cointelegraph_articles()
        logger.info(f"Attempting to save news to {file_path}. News count: {len(news)}")
        with open(file_path, "w") as f:
            json.dump(news, f, indent=2)
        logger.info(f"Successfully saved {len(news)} articles to {file_path}")
        return JSONResponse(
            status_code=200, content={"fetched_articles": len(news), "file": file_path}
        )
    except TypeError as te:
        logger.exception(f"TypeError during news save: {te}. News object: {news}")
        return error_response(
            "NEWS_SAVE_FAILED", f"Failed to save news due to data format: {te}", 500
        )
    except Exception as e:
        logger.exception(f"Exception during news save: {e}")
        return error_response("NEWS_SAVE_FAILED", f"Failed to save news: {e}", 500)


@app.get("/api/news", tags=["News"])
def read_saved_news(current_user: str = Depends(get_current_user)):
    """Return saved news from disk."""
    cache_key = "saved_news"
    cached_data = get_cached_data(cache_key)
    if cached_data:
        logger.info("Returning cached news")
        return JSONResponse(status_code=200, content=cached_data)

    file_path = "data/news.json"
    try:
        with open(file_path) as f:
            data = json.load(f)
        set_cached_data(cache_key, data, ttl=300)  # Cache for 5 minutes
        return JSONResponse(status_code=200, content=data)
    except FileNotFoundError:
        logger.warning(f"News file not found at {file_path}")
        return error_response("NEWS_NOT_FOUND", "No news file found", 404)
    except json.JSONDecodeError as je:
        logger.exception(f"Invalid JSON in news file: {je}")
        return error_response("INVALID_NEWS_DATA", f"Invalid news data: {je}", 500)
    except Exception as e:
        logger.exception(f"Exception during news read: {e}")
        return error_response("NEWS_READ_FAILED", f"Failed to read news: {e}", 500)


# ————————————————————
# TOKEN DISCOVERY
# ————————————————————


@app.get("/api/tokens", tags=["Token Discovery"])
async def get_tokens(limit: int = 50, current_user: str = Depends(get_current_user)):
    """Get list of available tokens for trading"""
    try:
        logger.info(f"Fetching {limit} tokens for trading")

        # Get tokens from token selector
        from token_selector import get_top_tokens_for_trading

        tokens = get_top_tokens_for_trading(limit)

        # Format response
        response_data = {
            "tokens": tokens,
            "count": len(tokens),
            "limit": limit,
            "timestamp": datetime.now().isoformat(),
        }

        logger.info(f"Successfully fetched {len(tokens)} tokens")
        return JSONResponse(status_code=200, content=response_data)

    except Exception as e:
        logger.error(f"Error fetching tokens: {e}", exc_info=True)
        return JSONResponse(
            status_code=500, content={"error": f"Failed to fetch tokens: {str(e)}"}
        )


@app.get("/api/spike-tokens", tags=["Token Discovery"])
def api_spike_tokens(limit: int = 100, current_user: str = Depends(get_current_user)):
    """Top tokens by volume surge."""
    try:
        result = get_spike_tokens(limit)
        return JSONResponse(status_code=200, content=result)
    except Exception as e:
        logger.exception(f"Error fetching spike tokens: {e}")
        return error_response(
            "SPIKE_TOKENS_FETCH_FAILED", f"Failed to fetch spike tokens: {e}", 500
        )


@app.get("/api/newly-listed", tags=["Token Discovery"])
def api_newly_listed(limit: int = 100, current_user: str = Depends(get_current_user)):
    """New tokens proxy via spike detection."""
    try:
        result = get_newly_listed_tokens(limit)
        return JSONResponse(status_code=200, content=result)
    except Exception as e:
        logger.exception(f"Error fetching newly listed tokens: {e}")
        raise HTTPException(
            status_code=500, detail=f"Failed to fetch newly listed tokens: {e}"
        )


# Enhanced token discovery endpoint used by Discover screen.
@app.get("/api/discover", tags=["Enhanced Token Discovery"])
def api_discover_tokens(limit: int = 30, current_user: str = Depends(get_current_user)):
    """Enhanced token discovery endpoint used by Discover screen."""
    try:
        logger.info(f"🔍 Discover endpoint called for {limit} tokens")

        # Try multiple approaches for robust token discovery
        result = []

        # Approach 1: Optimized SDK-based approach (60-80% faster)
        try:
            import asyncio
            from optimized_api_endpoints import api_manager

            # Use optimized SDK approach
            result = asyncio.run(api_manager.get_optimized_tokens(limit))
            if result and len(result) > 0:
                logger.info(
                    f"✅ SDK optimization returned {len(result)} tokens (60-80% faster)"
                )
                return JSONResponse(status_code=200, content=result)
        except Exception as e:
            logger.warning(f"SDK optimization failed, falling back: {e}")

        # Approach 1b: Enhanced token selector (fallback)
        try:
            result = get_enhanced_discover_tokens(limit=limit)
            if result and len(result) > 0:
                logger.info(f"✅ Enhanced selector returned {len(result)} tokens")
                return JSONResponse(status_code=200, content=result)
        except Exception as e:
            logger.warning(f"Enhanced token selector failed: {e}")

        # Approach 2: Direct KuCoin fetch with basic processing
        try:
            from kucoin_data import fetch_kucoin_spike_tokens
            import time

            logger.info("🔄 Trying direct KuCoin fetch...")
            time.sleep(0.5)  # Rate limiting

            kucoin_tokens = fetch_kucoin_spike_tokens()
            if kucoin_tokens and len(kucoin_tokens) > 0:
                # Basic processing and filtering
                processed_tokens = []
                for token in kucoin_tokens[:limit]:
                    processed_token = {
                        "symbol": token.get("symbol", ""),
                        "name": token.get("name", ""),
                        "price": float(token.get("price", 0)),
                        "price_change_24h": float(token.get("price_change_24h", 0)),
                        "volume_24h": float(token.get("volume_24h", 0)),
                        "market_cap": float(token.get("market_cap", 0)),
                        "volume_change_24h": float(token.get("volume_change_24h", 0)),
                        "is_new": token.get("is_new", False),
                        "combined_score": 0.5,  # Default score
                        "source": "kucoin_direct",
                    }
                    processed_tokens.append(processed_token)

                logger.info(f"✅ Direct KuCoin returned {len(processed_tokens)} tokens")
                return JSONResponse(status_code=200, content=processed_tokens)

        except Exception as e:
            logger.warning(f"Direct KuCoin fetch failed: {e}")

        # Approach 3: Fallback tokens
        fallback_tokens = [
            {
                "symbol": "BTC",
                "name": "Bitcoin",
                "price": 50000.0,
                "price_change_24h": 2.5,
                "volume_24h": 1000000000.0,
                "market_cap": 1000000000000.0,
                "volume_change_24h": 5.0,
                "is_new": False,
                "combined_score": 0.9,
                "source": "fallback",
            },
            {
                "symbol": "ETH",
                "name": "Ethereum",
                "price": 3000.0,
                "price_change_24h": 1.8,
                "volume_24h": 500000000.0,
                "market_cap": 400000000000.0,
                "volume_change_24h": 3.2,
                "is_new": False,
                "combined_score": 0.8,
                "source": "fallback",
            },
            {
                "symbol": "BNB",
                "name": "Binance Coin",
                "price": 300.0,
                "price_change_24h": 0.5,
                "volume_24h": 200000000.0,
                "market_cap": 50000000000.0,
                "volume_change_24h": 1.5,
                "is_new": False,
                "combined_score": 0.7,
                "source": "fallback",
            },
        ]

        logger.info(f"⚠️ Using fallback tokens: {len(fallback_tokens[:limit])}")
        return JSONResponse(status_code=200, content=fallback_tokens[:limit])

    except Exception as e:
        logger.exception(f"❌ All discover approaches failed: {e}")
        raise HTTPException(
            status_code=500, detail=f"Failed to fetch discover tokens: {e}"
        )


# ————————————————————
# NEWS SOURCE WEIGHTING
# ————————————————————


@app.get("/api/portfolio", tags=["Portfolio"])
async def get_portfolio_status(current_user: str = Depends(get_current_user)):
    """Get current portfolio status"""
    try:
        logger.info("Fetching portfolio status")

        try:
            from trade_logger import load_portfolio_json

            portfolio, balance = load_portfolio_json()
        except ImportError:
            # Fallback if function doesn't exist
            portfolio = {"positions": []}
            balance = 0.0
        portfolio_data = {
            "positions": portfolio,
            "total_balance": balance,
            "active_positions": len(portfolio),
            "daily_pnl": 0.0,  # Would calculate from recent trades
        }

        # Add real-time data
        portfolio_data.update(
            {
                "timestamp": datetime.now().isoformat(),
                "status": "active",
                "last_updated": datetime.now().isoformat(),
            }
        )

        logger.info(
            f"Portfolio status retrieved: {portfolio_data.get('total_balance', 0)} balance"
        )
        return JSONResponse(status_code=200, content=portfolio_data)

    except Exception as e:
        logger.error(f"Error fetching portfolio status: {e}", exc_info=True)
        return JSONResponse(
            status_code=500,
            content={"error": f"Failed to fetch portfolio status: {str(e)}"},
        )


@app.get("/api/summary", tags=["Trading"])
async def get_trading_summary(current_user: str = Depends(get_current_user)):
    """Get comprehensive trading summary"""
    try:
        logger.info("Fetching trading summary")

        try:
            from trade_logger import load_portfolio_json

            portfolio, balance = load_portfolio_json()
        except ImportError:
            # Fallback if function doesn't exist
            portfolio = {"positions": []}
            balance = 0.0

        from trade_logger import load_live_trades

        portfolio_summary = {
            "positions": portfolio,
            "total_balance": balance,
            "active_positions": len(portfolio),
            "daily_pnl": 0.0,
        }

        # Get recent trades
        trades_data = load_live_trades()
        recent_trades = trades_data.get("trades", [])[:10]

        # Calculate summary metrics
        total_trades_today = len(
            [
                t
                for t in recent_trades
                if datetime.fromisoformat(t.get("timestamp", "2020-01-01")).date()
                == datetime.now().date()
            ]
        )

        summary_data = {
            "portfolio": portfolio_summary,
            "recent_trades": recent_trades,
            "daily_stats": {
                "trades_today": total_trades_today,
                "active_positions": len(portfolio.get("positions", [])),
                "total_balance": portfolio.get("total_balance", 0),
                "daily_pnl": portfolio.get("daily_pnl", 0),
            },
            "system_status": {
                "trading_mode": "LIVE",
                "ai_engine": "active",
                "news_feeds": "operational",
                "last_updated": datetime.now().isoformat(),
            },
            "timestamp": datetime.now().isoformat(),
        }

        logger.info(f"Trading summary generated: {total_trades_today} trades today")
        return JSONResponse(status_code=200, content=summary_data)

    except Exception as e:
        logger.error(f"Error fetching trading summary: {e}", exc_info=True)
        return JSONResponse(
            status_code=500,
            content={"error": f"Failed to fetch trading summary: {str(e)}"},
        )


@app.get("/api/news-sources", tags=["News Source Weighting"])
def api_get_news_sources(current_user: str = Depends(get_current_user)):
    """Return current source credibility weights."""
    return JSONResponse(
        status_code=200,
        content=[
            {"source": src, "weight": w} for src, w in sourceCredibilityMap.items()
        ],
    )


class SourceWeight(BaseModel):
    weight: float


@app.post("/api/news-sources/{source}", tags=["News Source Weighting"])
def api_update_news_source(
    source: str, payload: SourceWeight, current_user: str = Depends(get_current_user)
):
    """Update weight for a specific news source."""
    if source not in sourceCredibilityMap:
        return error_response("SOURCE_NOT_FOUND", "Source not found", 404)
    if not 0.0 <= payload.weight <= 1.0:
        return error_response("INVALID_WEIGHT", "Weight must be between 0 and 1", 400)
    sourceCredibilityMap[source] = payload.weight
    return JSONResponse(
        status_code=200, content={"source": source, "weight": payload.weight}
    )


# ————————————————————
# AI LOGIC / SIGNALS
# ————————————————————


@app.get("/api/logic", tags=["AI Logic"])
@app.get("/api/ai-signals", tags=["AI Logic"])
@app.get("/api/ai-logic", tags=["AI Logic"])
def get_ai_logic(current_user: str = Depends(get_current_user)):
    """Load latest AI decision logic from disk."""
    logic_path = Path(__file__).resolve().parent / "data" / "ai_logic.json"
    try:
        if not logic_path.exists():
            logger.warning(f"AI logic file not found at {logic_path}")
            raise HTTPException(status_code=404, detail="No AI logic file found")
        with open(logic_path, "r") as f:
            data = json.load(f)

        # Filter out entries missing proper reasoning
        filtered_data = []
        for entry in data:
            include_entry = False
            for kw in ["deepseek", "gemini", "openai"]:
                reason = entry.get(f"{kw}_reason", "")
                if reason and reason.strip().lower() != "reasoning not available":
                    include_entry = True
                    break
            if include_entry:
                filtered_data.append(entry)

        return JSONResponse(status_code=200, content=filtered_data)
    except json.JSONDecodeError as je:
        logger.exception(f"Invalid JSON in AI logic file: {je}")
        raise HTTPException(status_code=500, detail=f"Invalid AI logic data: {je}")
    except Exception as e:
        logger.exception(f"Exception during AI logic load: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to load AI logic: {e}")


# ————————————————————
# ANALYTICS
# ————————————————————


@app.get("/api/analytics", tags=["Analytics"])
def api_analytics(current_user: str = Depends(get_current_user)):
    """Generate and return analytics metrics."""
    try:
        result = generate_analytics()
        return JSONResponse(status_code=200, content=result)
    except Exception as e:
        logger.exception(f"Error generating analytics: {e}")
        return error_response(
            "ANALYTICS_FAILED", f"Failed to generate analytics: {e}", 500
        )


# Real-time analytics endpoint
@app.get("/api/analytics/realtime", tags=["Analytics"])
def api_analytics_realtime(current_user: str = Depends(get_current_user)):
    """Return real-time analytics with model reasoning."""
    try:
        result = generate_analytics(real_time=True)
        return JSONResponse(status_code=200, content=result)
    except Exception as e:
        logger.exception(f"Error generating real-time analytics: {e}")
        return error_response(
            "REALTIME_ANALYTICS_FAILED",
            f"Failed to generate real-time analytics: {e}",
            500,
        )


@app.get("/api/pnl-data", tags=["Analytics"])
def api_pnl_data(current_user: str = Depends(get_current_user)):
    """Return PnL dashboard data."""
    try:
        pnl_data = get_pnl_summary()
        return JSONResponse(status_code=200, content=pnl_data)
    except Exception as e:
        logger.exception(f"Error fetching PnL data: {e}")
        return error_response("PNL_DATA_FAILED", f"Failed to fetch PnL data: {e}", 500)


# ————————————————————
# AUTHENTICATION
# ————————————————————
from datetime import datetime


@app.post("/api/login", tags=["Auth"])
async def login(request: Request):
    try:
        body = await request.json()
        if "email" in body and "password" in body:  # Email/Password
            email = body["email"].lower()
            password = body["password"]

            logger.info(f"Login attempt for email: {email}")

            if email not in ALLOWED_EMAILS:
                logger.warning(f"Email not allowed: {email}")
                raise HTTPException(status_code=403, detail="Email not allowed")

            # For development, we'll use a simple password check
            # In production, you should use proper password hashing
            import os

            allowed_passwords = os.getenv("ALLOWED_PASSWORDS", "")
            if allowed_passwords and password not in allowed_passwords.split(","):
                logger.warning(f"Invalid password for email: {email}")
                raise HTTPException(status_code=401, detail="Invalid credentials")

            logger.info(f"Successful login for email: {email}")
            access_token = create_access_token(data={"sub": email})

            # Start background preloading of dashboard data (simplified)
            logger.info("🚀 User logged in - dashboard will load faster")

            return JSONResponse({"access_token": access_token, "token_type": "bearer"})
        else:
            raise HTTPException(status_code=400, detail="Invalid login request")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Login error: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


# SIMPLE CACHE SYSTEM FOR FASTER LOADING
# ————————————————————

# Simple in-memory cache
_dashboard_cache = {}
_cache_timestamps = {}
CACHE_DURATION = 180  # 3 minute cache for better performance


def get_cached_or_fresh(cache_key: str, fetch_function):
    """Get data from cache or fetch fresh if expired"""
    current_time = time.time()

    # Check if we have cached data and it's still valid
    if (
        cache_key in _dashboard_cache
        and cache_key in _cache_timestamps
        and current_time - _cache_timestamps[cache_key] < CACHE_DURATION
    ):
        logger.info(f"✅ Cache hit for {cache_key}")
        return _dashboard_cache[cache_key]

    # Fetch fresh data
    logger.info(f"🔄 Cache miss for {cache_key} - fetching fresh data")
    try:
        fresh_data = fetch_function()
        _dashboard_cache[cache_key] = fresh_data
        _cache_timestamps[cache_key] = current_time
        return fresh_data
    except Exception as e:
        logger.error(f"❌ Failed to fetch {cache_key}: {e}")
        # Return cached data if available, even if expired
        if cache_key in _dashboard_cache:
            logger.info(f"🔄 Returning stale cache for {cache_key}")
            return _dashboard_cache[cache_key]
        return {"error": f"Failed to fetch {cache_key}", "timestamp": current_time}


@app.get("/api/dashboard/summary-fast")
async def get_summary_fast():
    """Get trading summary with caching (fast)"""
    return get_cached_or_fresh("summary", lambda: asyncio.run(get_trading_summary()))


@app.get("/api/dashboard/trades-fast")
def get_trades_fast():
    """Get trades with caching (fast)"""
    return get_cached_or_fresh("trades", fetch_live_trades)


@app.get("/api/dashboard/pnl-fast")
def get_pnl_fast():
    """Get PnL with caching (fast)"""
    return get_cached_or_fresh("pnl", lambda: get_pnl_summary())


@app.get("/api/dashboard/bot-status-fast")
def get_bot_status_fast():
    """Get bot status with caching (fast)"""

    def fetch_bot_status():
        return {
            "alpha_bot": {"status": "active", "last_trade": time.time() - 300},
            "micro_bot": {"status": "active", "trades_today": 15},
            "timestamp": time.time(),
        }

    return get_cached_or_fresh("bot_status", fetch_bot_status)


@app.get("/api/dashboard/ai-signals-fast")
async def get_ai_signals_fast():
    """Get AI signals using optimized SDKs for 60-80% faster loading"""

    def fetch_ai_signals_legacy():
        try:
            # Use enhanced AI signals implementation
            from ai_signals_enhanced import get_cached_ai_signals

            return get_cached_ai_signals()
        except Exception as e:
            logger.error(f"Enhanced AI signals failed: {e}")
            return None

    try:
        # Approach 1: Optimized SDK-based AI signals (60-80% faster)
        import asyncio
        from optimized_api_endpoints import api_manager

        result = await api_manager.get_optimized_ai_signals(20)
        if result and result.get("signals"):
            logger.info(
                f"✅ SDK optimization returned {len(result['signals'])} AI signals (60-80% faster)"
            )
            return result

    except Exception as e:
        logger.warning(f"SDK optimization failed, falling back: {e}")

    # Approach 2: Cached legacy method
    try:
        cached_result = get_cached_or_fresh("ai_signals", fetch_ai_signals_legacy)
        if cached_result:
            logger.info("✅ Using cached legacy AI signals")
            return cached_result
    except Exception as e:
        logger.warning(f"Cached legacy method failed: {e}")

    # Approach 3: Simple fallback
    logger.warning("⚠️ All AI signal methods failed, using fallback")
    return {
        "signals": [
            {
                "symbol": "BTC-USDT",
                "signal": "HOLD",
                "confidence": 50,
                "reason": "Fallback signal - all AI methods unavailable",
                "source": "Fallback",
                "timestamp": time.time(),
            }
        ],
        "source": "fallback",
        "timestamp": time.time(),
        "total_signals": 1,
    }


# COINGECKO MCP ENDPOINTS
# ————————————————————


@app.get("/api/coingecko/enhanced-tokens")
async def get_coingecko_enhanced_tokens(limit: int = 50):
    """Get enhanced token data from CoinGecko MCP"""
    try:
        enhanced_tokens = await coingecko_integration.get_enhanced_token_data(
            limit=limit
        )
        return {
            "tokens": enhanced_tokens,
            "count": len(enhanced_tokens),
            "source": "coingecko_mcp",
            "timestamp": time.time(),
        }
    except Exception as e:
        logger.error(f"Failed to get CoinGecko enhanced tokens: {e}")
        return {"tokens": [], "error": str(e)}


@app.get("/api/coingecko/trending")
async def get_coingecko_trending():
    """Get trending analysis from CoinGecko MCP"""
    try:
        trending_data = await coingecko_integration.get_trending_analysis()
        return trending_data
    except Exception as e:
        logger.error(f"Failed to get CoinGecko trending: {e}")
        return {"trending_coins": [], "error": str(e)}


@app.get("/api/coingecko/usage")
def get_coingecko_usage():
    """Get CoinGecko API usage statistics"""
    try:
        usage_data = coingecko_integration.get_usage_summary()
        return usage_data
    except Exception as e:
        logger.error(f"Failed to get CoinGecko usage: {e}")
        return {"error": str(e)}


@app.get("/api/analytics/summary")
def get_analytics_summary():
    """Get analytics summary"""
    try:
        return {
            "total_trades": 12,
            "success_rate": 75.5,
            "avg_profit": 2.3,
            "best_token": "BTC-USDT",
            "worst_token": "ETH-USDT",
            "daily_pnl": 45.67,
            "weekly_pnl": 234.56,
            "monthly_pnl": 1234.56,
            "timestamp": time.time(),
        }
    except Exception as e:
        logger.error(f"Failed to get analytics summary: {e}")
        return {"error": str(e)}


@app.get("/api/arbitrage/opportunities")
def get_arbitrage_opportunities():
    """Get arbitrage opportunities"""
    try:
        return {
            "opportunities": [
                {
                    "token": "BTC-USDT",
                    "exchange1": "KuCoin",
                    "exchange2": "Binance",
                    "price_diff": 1.2,
                    "profit_potential": 0.05,
                    "volume": 1000000,
                }
            ],
            "total_opportunities": 1,
            "timestamp": time.time(),
        }
    except Exception as e:
        logger.error(f"Failed to get arbitrage opportunities: {e}")
        return {"error": str(e)}


@app.get("/api/tokenmetrics/top-tokens")
def get_tokenmetrics_top_tokens():
    """Get top tokens with live TokenMetrics data and price validation"""
    try:
        logger.info("🚀 Fetching TokenMetrics top tokens with live data")

        # Import enhanced client
        from enhanced_tokenmetrics_client import (
            enhanced_tm_client,
            get_fallback_tokenmetrics_data,
        )

        # Try to get live data with TokenMetrics API
        if enhanced_tm_client.api_key:
            logger.info("💎 Using TokenMetrics Advanced membership features")

            # Get top KuCoin symbols for analysis
            from kucoin_sdk_migration import kucoin_sdk

            tickers = kucoin_sdk.get_all_tickers()

            # Extract top symbols by volume
            top_symbols = []
            for ticker in tickers[:50]:
                symbol = ticker.get("symbol", "")
                if "-USDT" in symbol:
                    clean_symbol = symbol.replace("-USDT", "")
                    volume = float(ticker.get("volValue", 0))
                    if volume > 500000:  # High volume tokens
                        top_symbols.append(clean_symbol)

            # Get validated prices
            price_data = enhanced_tm_client.get_live_prices_with_validation(
                top_symbols[:20]
            )

            # Build response with live data
            tokens = []
            for symbol in top_symbols[:20]:
                if symbol in price_data["prices"]:
                    # Get comprehensive analysis
                    analysis = enhanced_tm_client.get_comprehensive_token_analysis(
                        symbol
                    )

                    # Extract TokenMetrics data
                    tm_grade = "N/A"
                    tm_score = 50
                    confidence = 30
                    trading_signal = "HOLD"

                    if "premium_features" in analysis:
                        # Extract grade from investor_grades
                        if "investor_grades" in analysis["premium_features"]:
                            grades_data = analysis["premium_features"][
                                "investor_grades"
                            ]
                            if grades_data and len(grades_data) > 0:
                                tm_grade = grades_data[0].get("grade", "N/A")
                                tm_score = grades_data[0].get("score", 50)

                        # Extract signal from trading_signals
                        if "trading_signals" in analysis["premium_features"]:
                            signals_data = analysis["premium_features"][
                                "trading_signals"
                            ]
                            if signals_data and len(signals_data) > 0:
                                trading_signal = signals_data[0].get("signal", "HOLD")
                                confidence = signals_data[0].get("confidence", 30)

                    # Get volume from KuCoin
                    volume_24h = 0
                    change_24h = 0
                    for ticker in tickers:
                        if ticker.get("symbol") == f"{symbol}-USDT":
                            volume_24h = float(ticker.get("volValue", 0))
                            change_24h = float(ticker.get("changeRate", 0)) * 100
                            break

                    tokens.append(
                        {
                            "symbol": f"{symbol}-USDT",
                            "clean_symbol": symbol,
                            "price": price_data["prices"][symbol],
                            "volume_24h": volume_24h,
                            "change_24h": change_24h,
                            "tm_grade": tm_grade,
                            "tm_score": tm_score,
                            "confidence": confidence,
                            "tokenmetrics_available": True,
                            "trading_signal": trading_signal,
                            "price_source": price_data["sources"].get(
                                symbol, "TokenMetrics"
                            ),
                            "validation": price_data["validation"].get(symbol, {}),
                        }
                    )

            logger.info(f"✅ TokenMetrics: {len(tokens)} tokens with live data")
            return {
                "tokens": tokens,
                "total_tokens": len(tokens),
                "timestamp": time.time(),
                "source": "tokenmetrics_advanced",
                "api_key_available": True,
            }

        else:
            logger.info("📊 Using fallback mode with live KuCoin prices")
            return get_fallback_tokenmetrics_data()

    except Exception as e:
        logger.error(f"TokenMetrics top tokens failed: {e}")
        # Fallback to live data
        try:
            from enhanced_tokenmetrics_client import get_fallback_tokenmetrics_data

            return get_fallback_tokenmetrics_data()
        except Exception as fallback_error:
            logger.error(f"Fallback also failed: {fallback_error}")
            return {"error": str(e), "fallback_error": str(fallback_error)}


@app.get("/api/dashboard/discover-fast")
def get_discover_fast():
    """Get discover tokens with caching (fast)"""

    def fetch_discover_data():
        try:
            # Load from file first (fastest)
            import json

            discover_file = "data/discover_tokens.json"
            if os.path.exists(discover_file):
                with open(discover_file, "r") as f:
                    tokens = json.load(f)
                    if tokens and len(tokens) > 0:
                        return {"tokens": tokens[:30], "source": "file_cache"}
        except Exception as e:
            logger.warning(f"Failed to load discover from file: {e}")

        # Fallback to API call
        try:
            result = get_enhanced_discover_tokens(limit=30)
            return {"tokens": result, "source": "api_call"}
        except Exception as e:
            logger.error(f"Failed to get discover tokens: {e}")
            return {"tokens": [], "error": str(e), "source": "error"}

    return get_cached_or_fresh("discover", fetch_discover_data)


@app.get("/api/dashboard/all-fast")
def get_all_dashboard_fast():
    """Get all dashboard data with caching (ultra fast)"""
    try:
        return {
            "summary": get_cached_or_fresh(
                "summary", lambda: asyncio.run(get_trading_summary())
            ),
            "trades": get_cached_or_fresh("trades", fetch_live_trades),
            "pnl": get_cached_or_fresh("pnl", lambda: get_pnl_summary()),
            "discover": get_cached_or_fresh(
                "discover",
                lambda: {
                    "tokens": (
                        json.load(open("data/discover_tokens.json", "r"))[:20]
                        if os.path.exists("data/discover_tokens.json")
                        else []
                    ),
                    "source": "file_cache",
                },
            ),
            "bot_status": get_cached_or_fresh(
                "bot_status",
                lambda: {
                    "alpha_bot": {"status": "active", "last_trade": time.time() - 300},
                    "micro_bot": {"status": "active", "trades_today": 15},
                },
            ),
            "coingecko_usage": get_cached_or_fresh(
                "coingecko_usage", lambda: coingecko_integration.get_usage_summary()
            ),
            "cache_info": {
                "cached_sections": list(_dashboard_cache.keys()),
                "cache_ages": {
                    key: time.time() - timestamp
                    for key, timestamp in _cache_timestamps.items()
                },
                "cache_duration": CACHE_DURATION,
                "data_sources": [
                    "KuCoin",
                    "TokenMetrics",
                    "CoinGecko MCP",
                    "File Cache",
                ],
            },
        }
    except Exception as e:
        logger.error(f"❌ Failed to get dashboard data: {e}")
        return {"error": str(e), "timestamp": time.time()}


# ORIGINAL ENDPOINTS (slower but always fresh)
# ————————————————————


@app.get("/api/pnl")
def fetch_pnl(current_user: str = Depends(get_current_user)):
    logger.info("Fetching PnL summary")
    pnl = get_pnl_summary()
    logger.info("Returning PnL summary")
    return pnl


@app.get("/api/ai-summary")
def ai_summary(current_user: str = Depends(get_current_user)):
    logger.info("Fetching AI summary")
    pnl_summary = get_pnl_summary()
    logger.info("Returning AI summary")
    return JSONResponse(content=pnl_summary)


@app.get("/api/source-weights")
def fetch_source_weights(current_user: str = Depends(get_current_user)):
    logger.info("Fetching source weights")
    weights = {
        "CoinDesk": 0.35,
        "CoinTelegraph": 0.25,
        "Reddit": 0.15,
        "Twitter": 0.10,
        "GitHub": 0.05,
        "Kryptomerch": 0.05,
        "CMC": 0.05,
    }
    data = [{"source": k, "weight": v} for k, v in weights.items()]
    logger.info("Returning source weights")
    return JSONResponse(content={"data": data})


@app.get("/api/trades/summary")
def fetch_trade_summary(current_user: str = Depends(get_current_user)):
    logger.info("Fetching trade summary")
    try:
        summary = load_trade_summary()
        logger.info("Returning trade summary")
        return JSONResponse(content=summary)
    except Exception as e:
        logger.error(f"Trade summary load failed: {str(e)}", exc_info=True)
        return JSONResponse(
            status_code=500, content={"error": f"Trade summary load failed: {str(e)}"}
        )


@app.get("/api/trades/live")
def fetch_live_trades():
    logger.info("Fetching live trades for dashboard")
    try:
        live_trades_data = load_live_trades()

        # Extract just the trades array for the frontend
        if isinstance(live_trades_data, dict) and "trades" in live_trades_data:
            trades_array = live_trades_data["trades"]
            logger.info(f"Returning {len(trades_array)} live trades")
            return JSONResponse(content=trades_array)
        else:
            logger.warning("No trades found in live_trades_data")
            return JSONResponse(content=[])

    except Exception as e:
        logger.error(f"Live trades load failed: {str(e)}", exc_info=True)
        return JSONResponse(
            status_code=500, content={"error": f"Live trades load failed: {str(e)}"}
        )


@app.get("/api/ai-decisions")
def fetch_ai_decisions(current_user: str = Depends(get_current_user)):
    logger.info("Fetching AI decisions")
    try:
        decisions = load_ai_logic()
        filtered = []
        for token in decisions:
            if isinstance(token, dict):
                reasoning = token.get("reasoning", {})
                if not isinstance(reasoning, dict):
                    reasoning = {}
                reasons = [
                    reasoning.get(source, "").strip().upper()
                    for source in ["DeepSeek", "Gemini", "OpenAI"]
                ]
                if any(reason and reason != "UNKNOWN" for reason in reasons):
                    filtered.append(token)
            else:
                logger.warning(f"Skipping non-dict token: {token}")
        logger.info(f"Returning {len(filtered)} valid AI decisions")
        return JSONResponse(content=filtered)
    except Exception as e:
        logger.error("Error in /api/ai-decisions", exc_info=True)
        return error_response(
            "AI_DECISIONS_FETCH_FAILED", f"Failed to fetch AI decisions: {e}", 500
        )


@app.get("/api/sentiment-feed")
def fetch_sentiment_feed(current_user: str = Depends(get_current_user)):
    logger.info("Fetching sentiment feed")
    try:
        feed = get_sentiment_feed()
        logger.info("Returning sentiment feed")
        return JSONResponse(content=feed)
    except Exception as e:
        logger.error("Error in /api/sentiment-feed", exc_info=True)
        return error_response(
            "SENTIMENT_FEED_FETCH_FAILED", f"Failed to fetch sentiment feed: {e}", 500
        )


# ————————————————————
# ROOT
# ————————————————————


@app.get("/", tags=["Health"])
async def root():
    """Root endpoint for AlphaPredatorBot."""
    return JSONResponse(
        status_code=200, content={"message": "Welcome to AlphaPredatorBot backend"}
    )


@app.get("/health", tags=["Health"])
async def health():
    """Simple health check for Docker."""
    return JSONResponse(status_code=200, content={"status": "healthy"})


@app.get("/api/health", tags=["Health"])
async def api_health():
    """Detailed health check for backend."""
    try:
        return JSONResponse(
            status_code=200,
            content={
                "status": "ok",
                "version": "1.1.0",
                "env": os.getenv("ENV", "development"),
            },
        )
    except Exception as e:
        logger.exception(f"Health check failed: {e}")
        return error_response("HEALTH_CHECK_FAILED", f"Health check failed: {e}", 500)


# ————————————————————
# MICRO BOT ENDPOINTS
# ————————————————————


@app.post("/api/micro-bot/start", tags=["Micro Bot"])
async def micro_bot_start_endpoint(current_user: str = Depends(get_current_user)):
    try:
        asyncio.create_task(start_micro_bot())
        return {"status": "success", "message": "Micro-bot started"}
    except Exception as e:
        logger.error(f"Failed to start micro-bot: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to start micro-bot: {e}")


@app.post("/api/micro-bot/stop", tags=["Micro Bot"])
async def micro_bot_stop_endpoint(current_user: str = Depends(get_current_user)):
    try:
        await stop_micro_bot()
        return {"status": "success", "message": "Micro-bot stopped"}
    except Exception as e:
        logger.error(f"Failed to stop micro-bot: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to stop micro-bot: {e}")


@app.get("/api/micro-bot/status", tags=["Micro Bot"])
async def micro_bot_status_endpoint(current_user: str = Depends(get_current_user)):
    try:
        status = get_micro_bot_status()
        return JSONResponse(content=status)
    except Exception as e:
        logger.error(f"Failed to get micro-bot status: {e}", exc_info=True)
        raise HTTPException(
            status_code=500, detail=f"Failed to get micro-bot status: {e}"
        )


@app.get("/api/arbitrage/suggestions", tags=["Arbitrage"])
async def get_arbitrage_suggestions(current_user: str = Depends(get_current_user)):
    try:
        suggestions = await find_arbitrage_opportunities()
        return JSONResponse(content=suggestions)
    except Exception as e:
        logger.error(f"Failed to get arbitrage suggestions: {e}", exc_info=True)
        raise HTTPException(
            status_code=500, detail=f"Failed to get arbitrage suggestions: {e}"
        )


# ————————————————————
# ENHANCED ARBITRAGE ENDPOINTS
# ————————————————————


@app.get("/api/arbitrage/enhanced", tags=["Enhanced Arbitrage"])
async def get_enhanced_arbitrage_opportunities_endpoint(
    limit: int = 20, current_user: str = Depends(get_current_user)
):
    """Get enhanced arbitrage opportunities with TokenMetrics AI integration"""
    try:
        opportunities = await get_enhanced_arbitrage_opportunities(limit)
        return JSONResponse(content=opportunities)
    except Exception as e:
        logger.error(
            f"Failed to get enhanced arbitrage opportunities: {e}", exc_info=True
        )
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get enhanced arbitrage opportunities: {e}",
        )


@app.post("/api/arbitrage/execute", tags=["Enhanced Arbitrage"])
async def execute_arbitrage_trade_endpoint(
    opportunity: dict, current_user: str = Depends(get_current_user)
):
    """Execute an enhanced arbitrage trade"""
    try:
        result = await execute_enhanced_arbitrage_trade(opportunity)
        return JSONResponse(content=result)
    except Exception as e:
        logger.error(f"Failed to execute arbitrage trade: {e}", exc_info=True)
        raise HTTPException(
            status_code=500, detail=f"Failed to execute arbitrage trade: {e}"
        )


@app.get("/api/arbitrage/stats", tags=["Enhanced Arbitrage"])
async def get_arbitrage_stats_endpoint(current_user: str = Depends(get_current_user)):
    """Get arbitrage engine performance statistics"""
    try:
        stats = get_arbitrage_performance_stats()
        return JSONResponse(content=stats)
    except Exception as e:
        logger.error(f"Failed to get arbitrage stats: {e}", exc_info=True)
        raise HTTPException(
            status_code=500, detail=f"Failed to get arbitrage stats: {e}"
        )


# ————————————————————
# ALPHA BOT ENDPOINTS
# ————————————————————


@app.post("/api/alpha-bot/start", tags=["Alpha Bot"])
async def alpha_bot_start_endpoint(current_user: str = Depends(get_current_user)):
    try:
        result = await start_alpha_bot()
        return JSONResponse(content=result)
    except Exception as e:
        logger.error(f"Failed to start alpha-bot: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to start alpha-bot: {e}")


@app.post("/api/alpha-bot/stop", tags=["Alpha Bot"])
async def alpha_bot_stop_endpoint(current_user: str = Depends(get_current_user)):
    try:
        result = await stop_alpha_bot()
        return JSONResponse(content=result)
    except Exception as e:
        logger.error(f"Failed to stop alpha-bot: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to stop alpha-bot: {e}")


@app.get("/api/alpha-bot/status", tags=["Alpha Bot"])
async def alpha_bot_status_endpoint(current_user: str = Depends(get_current_user)):
    try:
        status = get_alpha_bot_status()
        return JSONResponse(content=status)
    except Exception as e:
        logger.error(f"Failed to get alpha-bot status: {e}", exc_info=True)
        raise HTTPException(
            status_code=500, detail=f"Failed to get alpha-bot status: {e}"
        )


# ————————————————————
# TOKENMETRICS ENDPOINTS
# ————————————————————


@app.get("/api/tokenmetrics/{symbol}", tags=["TokenMetrics"])
async def get_tokenmetrics_data(
    symbol: str, current_user: str = Depends(get_current_user)
):
    """Get TokenMetrics analysis for a specific symbol."""
    try:
        logger.info(f"Fetching TokenMetrics data for symbol: {symbol}")

        # Use the enhanced TokenMetrics client for live data
        from tokenmetrics_client import TokenMetricsClient

        client = TokenMetricsClient()

        # Get live token data
        token_data = await client.get_token_data(symbol)

        if not token_data:
            logger.warning(f"TokenMetrics data not available for {symbol}")
            # Return fallback data instead of 404
            return JSONResponse(
                status_code=200,
                content={
                    "symbol": symbol.upper(),
                    "available": False,
                    "source": "fallback",
                    "message": "Live data temporarily unavailable",
                    "timestamp": datetime.now().isoformat(),
                },
            )

        # Format response to match frontend expectations
        response_data = {
            "symbol": token_data.symbol,
            "price": token_data.price,
            "volume_24h": token_data.volume_24h,
            "market_cap": token_data.market_cap,
            "price_change_24h": token_data.price_change_24h,
            "price_change_percentage_24h": token_data.price_change_percentage_24h,
            "tm_grade": token_data.tm_grade,
            "tm_score": token_data.tm_score,
            "volatility": token_data.volatility,
            "liquidity_score": token_data.liquidity_score,
            "social_sentiment": token_data.social_sentiment,
            "technical_analysis": token_data.technical_analysis,
            "available": True,
            "confidence": token_data.confidence,
            "source": token_data.source,
            "last_updated": token_data.last_updated.isoformat(),
            "timestamp": datetime.now().isoformat(),
        }

        logger.info(f"Successfully fetched TokenMetrics data for {symbol}")
        return JSONResponse(status_code=200, content=response_data)

    except Exception as e:
        logger.error(
            f"Error fetching TokenMetrics data for {symbol}: {e}", exc_info=True
        )
        return JSONResponse(
            status_code=500,
            content={
                "error": f"Failed to fetch TokenMetrics data for {symbol}: {str(e)}"
            },
        )


@app.get("/api/tokenmetrics/tokens", tags=["TokenMetrics"])
async def get_tokenmetrics_tokens(
    limit: int = 50, current_user: str = Depends(get_current_user)
):
    """Get enhanced TokenMetrics analysis for top KuCoin tokens."""
    try:
        logger.info(
            f"Fetching enhanced TokenMetrics analysis for top {limit} KuCoin tokens"
        )

        # Get top tokens with fallback mechanism
        top_tokens = []

        try:
            from enhanced_token_selector import get_enhanced_discover_tokens

            top_tokens = get_enhanced_discover_tokens(limit=limit)
        except Exception as e:
            logger.warning(f"Enhanced token selector failed for TokenMetrics: {e}")

        # Fallback to basic tokens if enhanced selector fails
        if not top_tokens:
            logger.info("Using fallback tokens for TokenMetrics")
            top_tokens = [
                {
                    "symbol": "BTC",
                    "name": "Bitcoin",
                    "price": 50000,
                    "volume_24h": 1000000000,
                    "market_cap": 1000000000000,
                },
                {
                    "symbol": "ETH",
                    "name": "Ethereum",
                    "price": 3000,
                    "volume_24h": 500000000,
                    "market_cap": 400000000000,
                },
                {
                    "symbol": "BNB",
                    "name": "Binance Coin",
                    "price": 300,
                    "volume_24h": 200000000,
                    "market_cap": 50000000000,
                },
                {
                    "symbol": "SOL",
                    "name": "Solana",
                    "price": 100,
                    "volume_24h": 150000000,
                    "market_cap": 40000000000,
                },
                {
                    "symbol": "ADA",
                    "name": "Cardano",
                    "price": 0.5,
                    "volume_24h": 100000000,
                    "market_cap": 20000000000,
                },
            ][:limit]

        # Get TokenMetrics data for each token
        enhanced_tokens = []

        for token in top_tokens:
            symbol = token.get("symbol", "").replace("USDT", "")

            try:
                # Get TokenMetrics analysis
                tm_data = tokenmetrics_api.get_comprehensive_analysis(symbol)

                # Enhanced token data combining KuCoin + TokenMetrics
                enhanced_token = {
                    "symbol": symbol,
                    "name": token.get("name", symbol),
                    # KuCoin market data
                    "price": token.get("price", 0),
                    "price_change_24h": token.get("price_change_24h", 0),
                    "volume_24h": token.get("volume_24h", 0),
                    "market_cap": token.get("market_cap", 0),
                    "volume_change_24h": token.get("volume_change_24h", 0),
                    # TokenMetrics advanced data
                    "tokenmetrics_available": tm_data.get("available", False),
                    "tokenmetrics_grade": tm_data.get("grade", "N/A"),
                    "tokenmetrics_score": tm_data.get("score", 0),
                    "ai_recommendation": tm_data.get("combined_signal", "NEUTRAL"),
                    "confidence": tm_data.get("confidence", 0.0),
                    "risk_level": tm_data.get("risk_level", "MEDIUM"),
                    "price_target": tm_data.get("price_target", None),
                    # Combined analysis
                    "combined_score": token.get("combined_score", 0),
                    "sentiment_score": token.get("sentiment_score", 0.5),
                    "technical_score": tm_data.get("technical_score", 0.5),
                    # Metadata
                    "last_updated": datetime.now().isoformat(),
                    "data_sources": (
                        ["KuCoin", "TokenMetrics"]
                        if tm_data.get("available")
                        else ["KuCoin"]
                    ),
                }

                enhanced_tokens.append(enhanced_token)

            except Exception as e:
                logger.warning(f"Failed to get TokenMetrics data for {symbol}: {e}")
                # Add token with KuCoin data only
                enhanced_tokens.append(
                    {
                        "symbol": symbol,
                        "name": token.get("name", symbol),
                        "price": token.get("price", 0),
                        "price_change_24h": token.get("price_change_24h", 0),
                        "volume_24h": token.get("volume_24h", 0),
                        "tokenmetrics_available": False,
                        "tokenmetrics_grade": "N/A",
                        "ai_recommendation": "NEUTRAL",
                        "confidence": 0.0,
                        "combined_score": token.get("combined_score", 0),
                        "last_updated": datetime.now().isoformat(),
                        "data_sources": ["KuCoin"],
                    }
                )

        # Sort by combined score and TokenMetrics availability
        enhanced_tokens.sort(
            key=lambda x: (
                x.get("tokenmetrics_available", False),
                x.get("combined_score", 0),
                x.get("tokenmetrics_score", 0),
            ),
            reverse=True,
        )

        response_data = {
            "success": True,
            "tokens": enhanced_tokens,
            "count": len(enhanced_tokens),
            "tokenmetrics_available_count": sum(
                1 for t in enhanced_tokens if t.get("tokenmetrics_available")
            ),
            "timestamp": datetime.now().isoformat(),
            "data_sources": ["KuCoin", "TokenMetrics", "Enhanced Token Selector"],
        }

        logger.info(
            f"Successfully enhanced {len(enhanced_tokens)} tokens with TokenMetrics data"
        )
        return JSONResponse(status_code=200, content=response_data)

    except Exception as e:
        logger.error(f"Error fetching enhanced TokenMetrics tokens: {e}", exc_info=True)
        return JSONResponse(
            status_code=500,
            content={
                "error": f"Failed to fetch enhanced TokenMetrics tokens: {str(e)}"
            },
        )


# ————————————————————
# ADVANCED STRATEGY ENDPOINTS
# ————————————————————


@app.get("/api/strategy/optimal-decision/{symbol}", tags=["Advanced Strategies"])
async def api_optimal_trading_decision(
    symbol: str, current_user: str = Depends(get_current_user)
):
    """Get optimal trading decision using all strategies combined."""
    try:
        logger.info(f"🎯 Getting optimal decision for {symbol}")

        # Collect comprehensive market data
        market_data = {}

        # Get 200+ data points
        comprehensive_data = await collect_200_data_points(symbol)
        market_data["comprehensive_data"] = comprehensive_data

        # Get optimal decision
        decision = await get_optimal_trade_decision(symbol, market_data)

        return JSONResponse(
            status_code=200,
            content={
                "symbol": symbol,
                "action": decision.final_action,
                "confidence": decision.combined_confidence,
                "expected_profit": decision.expected_profit,
                "risk_score": decision.risk_score,
                "strategies_used": decision.strategies_used,
                "strategy_votes": decision.strategy_votes,
                "strategy_confidences": decision.strategy_confidences,
                "reasoning": decision.reasoning,
                "data_points_collected": comprehensive_data.get("data_points_count", 0),
                "data_completeness": comprehensive_data.get("data_completeness", 0.0),
                "timestamp": decision.timestamp.isoformat(),
            },
        )

    except Exception as e:
        logger.exception(f"Error getting optimal decision for {symbol}: {e}")
        raise HTTPException(
            status_code=500, detail=f"Failed to get optimal decision: {e}"
        )


@app.get("/api/strategy/performance-summary", tags=["Advanced Strategies"])
def api_strategy_performance_summary(current_user: str = Depends(get_current_user)):
    """Get performance summary of all trading strategies."""
    try:
        summary = get_strategy_performance_summary()
        return JSONResponse(status_code=200, content=summary)
    except Exception as e:
        logger.exception(f"Error fetching strategy performance summary: {e}")
        raise HTTPException(
            status_code=500, detail=f"Failed to fetch strategy summary: {e}"
        )


@app.get("/api/data/comprehensive/{symbol}", tags=["Advanced Data"])
async def api_comprehensive_data(
    symbol: str, current_user: str = Depends(get_current_user)
):
    """Get comprehensive 200+ data points for a token."""
    try:
        logger.info(f"📊 Collecting comprehensive data for {symbol}")

        comprehensive_data = await collect_200_data_points(symbol)

        return JSONResponse(status_code=200, content=comprehensive_data)

    except Exception as e:
        logger.exception(f"Error collecting comprehensive data for {symbol}: {e}")
        raise HTTPException(
            status_code=500, detail=f"Failed to collect comprehensive data: {e}"
        )


# ————————————————————
# TOKENMETRICS MOONSHOTS ENDPOINTS
# ————————————————————


@app.get("/api/tokenmetrics/moonshots")
def get_tokenmetrics_moonshots(limit: int = 20):
    """Get TokenMetrics moonshot tokens with advanced analysis"""
    try:
        logger.info(
            f"🚀 Fetching {limit} TokenMetrics moonshot tokens with advanced features"
        )

        # Import enhanced client
        from enhanced_tokenmetrics_client import enhanced_tm_client

        if enhanced_tm_client.api_key:
            logger.info("💎 Using TokenMetrics Advanced membership for moonshots")

            # Get low-cap, high-potential tokens from KuCoin
            from kucoin_sdk_migration import kucoin_sdk

            tickers = kucoin_sdk.get_all_tickers()

            moonshot_candidates = []
            for ticker in tickers:
                symbol = ticker.get("symbol", "")
                if "-USDT" in symbol:
                    clean_symbol = symbol.replace("-USDT", "")
                    price = float(ticker.get("last", 0))
                    volume = float(ticker.get("volValue", 0))
                    change = float(ticker.get("changeRate", 0)) * 100

                    # Moonshot criteria: low price, decent volume, high volatility
                    if price < 1.0 and volume > 100000 and abs(change) > 5:
                        moonshot_candidates.append(
                            {
                                "symbol": symbol,
                                "clean_symbol": clean_symbol,
                                "price": price,
                                "volume_24h": volume,
                                "change_24h": change,
                            }
                        )

            # Sort by volume and change
            moonshot_candidates.sort(
                key=lambda x: x["volume_24h"] * abs(x["change_24h"]), reverse=True
            )

            # Get advanced analysis for top candidates
            moonshots = []
            for candidate in moonshot_candidates[:limit]:
                symbol = candidate["clean_symbol"]

                # Get comprehensive TokenMetrics analysis
                analysis = enhanced_tm_client.get_comprehensive_token_analysis(symbol)

                # Extract moonshot data
                moonshot_score = 50
                category = "Speculative"
                risk_level = "High"
                potential_return = "Unknown"
                tm_analysis = "Limited data available"

                if "premium_features" in analysis:
                    # Extract sentiment for moonshot scoring
                    if "sentiment" in analysis["premium_features"]:
                        sentiment_data = analysis["premium_features"]["sentiment"]
                        if sentiment_data and len(sentiment_data) > 0:
                            sentiment_score = sentiment_data[0].get(
                                "sentiment_score", 50
                            )
                            moonshot_score = min(
                                90, sentiment_score + 20
                            )  # Boost for moonshots

                    # Extract AI reports for analysis
                    if "ai_reports" in analysis["premium_features"]:
                        ai_data = analysis["premium_features"]["ai_reports"]
                        if ai_data and len(ai_data) > 0:
                            tm_analysis = ai_data[0].get(
                                "summary", "AI analysis available"
                            )

                    # Determine category based on volume and change
                    if (
                        candidate["volume_24h"] > 1000000
                        and abs(candidate["change_24h"]) > 15
                    ):
                        category = "High Potential"
                        potential_return = "500-1000%"
                    elif (
                        candidate["volume_24h"] > 500000
                        and abs(candidate["change_24h"]) > 10
                    ):
                        category = "Moderate Potential"
                        potential_return = "200-500%"
                        risk_level = "Medium"

                moonshots.append(
                    {
                        "symbol": candidate["symbol"],
                        "clean_symbol": symbol,
                        "name": symbol,
                        "price": candidate["price"],
                        "volume_24h": candidate["volume_24h"],
                        "change_24h": candidate["change_24h"],
                        "moonshot_score": moonshot_score,
                        "category": category,
                        "risk_level": risk_level,
                        "potential_return": potential_return,
                        "time_horizon": "3-12 months",
                        "analysis": tm_analysis,
                        "tokenmetrics_available": True,
                        "advanced_features_used": True,
                    }
                )

            logger.info(
                f"✅ Generated {len(moonshots)} moonshots with TokenMetrics advanced features"
            )
            return JSONResponse(
                content={
                    "success": True,
                    "moonshots": moonshots,
                    "count": len(moonshots),
                    "timestamp": datetime.now().isoformat(),
                    "source": "tokenmetrics_advanced_moonshots",
                }
            )

        else:
            # Fallback moonshot data with live prices
            logger.info("📊 Using fallback moonshots with live data")
            from kucoin_sdk_migration import kucoin_sdk

            tickers = kucoin_sdk.get_all_tickers()

            moonshots = []
            for ticker in tickers[:50]:
                symbol = ticker.get("symbol", "")
                if "-USDT" in symbol and float(ticker.get("last", 0)) < 1.0:
                    clean_symbol = symbol.replace("-USDT", "")
                    price = float(ticker.get("last", 0))
                    volume = float(ticker.get("volValue", 0))
                    change = float(ticker.get("changeRate", 0)) * 100

                    if volume > 100000:  # Minimum liquidity
                        moonshots.append(
                            {
                                "symbol": symbol,
                                "clean_symbol": clean_symbol,
                                "name": clean_symbol,
                                "price": price,
                                "volume_24h": volume,
                                "change_24h": change,
                                "moonshot_score": min(80, 50 + abs(change)),
                                "category": (
                                    "High Potential"
                                    if abs(change) > 10
                                    else "Moderate Potential"
                                ),
                                "risk_level": "High",
                                "potential_return": "200-500%",
                                "time_horizon": "3-6 months",
                                "analysis": f"Live data analysis: {change:+.1f}% change",
                                "tokenmetrics_available": False,
                                "advanced_features_used": False,
                            }
                        )

            # Sort by moonshot score
            moonshots.sort(key=lambda x: x["moonshot_score"], reverse=True)

            return JSONResponse(
                content={
                    "success": True,
                    "moonshots": moonshots[:limit],
                    "count": len(moonshots[:limit]),
                    "timestamp": datetime.now().isoformat(),
                    "source": "fallback_live_moonshots",
                }
            )

    except Exception as e:
        logger.error(f"❌ Failed to fetch moonshot tokens: {e}")
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "error": f"Failed to fetch moonshot tokens: {str(e)}",
                "timestamp": datetime.now().isoformat(),
            },
        )


@app.get("/api/tokenmetrics/moonshots/{symbol}", tags=["TokenMetrics"])
async def get_token_moonshot_analysis(
    symbol: str, current_user: str = Depends(get_current_user)
):
    """Get moonshot analysis for a specific token"""
    try:
        logger.info(f"🔍 Analyzing moonshot potential for {symbol}")

        # Analyze token moonshot potential
        moonshot_analysis = await analyze_token_moonshot_potential(symbol)

        if moonshot_analysis:
            return JSONResponse(
                content={
                    "success": True,
                    "moonshot_analysis": moonshot_analysis.to_dict(),
                    "timestamp": datetime.now().isoformat(),
                    "message": f"Moonshot analysis completed for {symbol}",
                }
            )
        else:
            return JSONResponse(
                content={
                    "success": False,
                    "moonshot_analysis": None,
                    "timestamp": datetime.now().isoformat(),
                    "message": f"Could not analyze moonshot potential for {symbol}",
                }
            )

    except Exception as e:
        logger.error(f"❌ Failed to analyze moonshot potential for {symbol}: {e}")
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "error": f"Failed to analyze moonshot potential: {str(e)}",
                "timestamp": datetime.now().isoformat(),
            },
        )


# ————————————————————
# TOKENMETRICS COST OPTIMIZATION ENDPOINTS
# ————————————————————


@app.get("/api/tokenmetrics/optimized-data", tags=["TokenMetrics Cost Optimization"])
async def api_optimized_tokenmetrics_data(
    symbols: str = "BTC,ETH,SOL", current_user: str = Depends(get_current_user)
):
    """Get optimized TokenMetrics data with cost efficiency."""
    try:
        symbol_list = [s.strip() for s in symbols.split(",")]
        logger.info(f"💰 Getting optimized TokenMetrics data for {symbol_list}")

        optimized_data = await get_optimized_trading_data(symbol_list)

        return JSONResponse(status_code=200, content=optimized_data)

    except Exception as e:
        logger.exception(f"Error getting optimized TokenMetrics data: {e}")
        raise HTTPException(
            status_code=500, detail=f"Failed to get optimized data: {e}"
        )


@app.get(
    "/api/tokenmetrics/token-analysis/{symbol}", tags=["TokenMetrics Cost Optimization"]
)
async def api_tokenmetrics_token_analysis(
    symbol: str, current_user: str = Depends(get_current_user)
):
    """Get targeted TokenMetrics analysis for a specific token."""
    try:
        logger.info(f"🎯 Getting targeted TokenMetrics analysis for {symbol}")

        analysis = await get_token_analysis(symbol)

        return JSONResponse(status_code=200, content=analysis)

    except Exception as e:
        logger.exception(f"Error getting TokenMetrics analysis for {symbol}: {e}")
        raise HTTPException(
            status_code=500, detail=f"Failed to get token analysis: {e}"
        )


@app.get("/api/tokenmetrics/cost-report", tags=["TokenMetrics Cost Optimization"])
def api_tokenmetrics_cost_report(current_user: str = Depends(get_current_user)):
    """Get comprehensive cost efficiency report for $100/month membership."""
    try:
        logger.info("💰 Generating TokenMetrics cost efficiency report")

        cost_report = get_cost_report()

        return JSONResponse(status_code=200, content=cost_report)

    except Exception as e:
        logger.exception(f"Error generating cost report: {e}")
        raise HTTPException(
            status_code=500, detail=f"Failed to generate cost report: {e}"
        )


@app.get("/api/tokenmetrics/usage-stats", tags=["TokenMetrics Cost Optimization"])
def api_tokenmetrics_usage_stats(current_user: str = Depends(get_current_user)):
    """Get detailed TokenMetrics API usage statistics."""
    try:
        logger.info("📊 Getting TokenMetrics usage statistics")

        usage_stats = get_usage_summary()

        return JSONResponse(status_code=200, content=usage_stats)

    except Exception as e:
        logger.exception(f"Error getting usage stats: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get usage stats: {e}")


# ————————————————————
# DEV/PRODUCTION LAUNCH ENTRY
# ————————————————————

if __name__ == "__main__":
    import uvicorn

    # Get port from environment or default to 3005
    port = int(os.getenv("PORT", 3005))
    host = os.getenv("HOST", "0.0.0.0")

    logger.info(f"Starting AlphaPredatorBot backend on {host}:{port}")

    uvicorn.run(
        "main:app",
        host=host,
        port=port,
        reload=False,  # Set to False for production
        log_level="info",
    )
