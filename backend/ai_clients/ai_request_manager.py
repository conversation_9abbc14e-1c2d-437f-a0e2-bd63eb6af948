import time
import logging
import json
import requests
import asyncio
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed

# Safe imports with fallbacks to prevent crashes
try:
    from ai_clients.openai_client import call_openai
except Exception as e:
    logging.warning(f"OpenAI client import failed: {e}")

    def call_openai(*args, **kwargs):
        return {
            "decision": "HOLD",
            "confidence": 50.0,
            "reason": "OpenAI client unavailable",
        }


try:
    from ai_clients.real_gemini_client import call_gemini
except Exception as e:
    logging.warning(f"Gemini client import failed: {e}")

    def call_gemini(*args, **kwargs):
        return {
            "decision": "HOLD",
            "confidence": 50.0,
            "reason": "Gemini client unavailable",
        }


try:
    from ai_clients.real_deepseek_client import call_deepseek
except Exception as e:
    logging.warning(f"DeepSeek client import failed: {e}")

    def call_deepseek(*args, **kwargs):
        return {
            "decision": "HOLD",
            "confidence": 50.0,
            "reason": "DeepSeek client unavailable",
        }


try:
    from ai_clients.claude_client import call_claude
except Exception as e:
    logging.warning(f"Claude client import failed: {e}")

    def call_claude(*args, **kwargs):
        return {
            "decision": "HOLD",
            "confidence": 50.0,
            "reason": "Claude client unavailable",
        }


from ai_clients.model_selector import (
    TaskType,
    Urgency,
    Complexity,
    select_optimal_model,
    get_cost_estimate,
)
from model_weights import load_model_weights

# Import cost tracking
try:
    from real_time_cost_monitor import record_api_usage

    COST_TRACKING_AVAILABLE = True
except ImportError:
    COST_TRACKING_AVAILABLE = False

    def record_api_usage(*args, **kwargs):
        pass


logger = logging.getLogger(__name__)

# Enhanced rate limiting configuration for high-frequency trading
RATE_LIMIT_CONFIG = {
    "DeepSeek": {
        "base_cooldown": 5,
        "max_cooldown": 60,
        "requests_per_minute": 25,
        "max_requests_per_hour": 800,
        "burst_limit": 5,
        "priority": 3,
    },
    "Claude": {
        "base_cooldown": 3,
        "max_cooldown": 30,
        "requests_per_minute": 40,
        "max_requests_per_hour": 1200,
        "burst_limit": 8,
        "priority": 1,
    },
    "OpenAI": {
        "base_cooldown": 2,
        "max_cooldown": 20,
        "requests_per_minute": 50,
        "max_requests_per_hour": 2000,
        "burst_limit": 10,
        "priority": 2,
    },
    "Gemini": {
        "base_cooldown": 10,
        "max_cooldown": 120,
        "requests_per_minute": 15,
        "max_requests_per_hour": 300,
        "burst_limit": 3,
        "priority": 4,
    },
}

# Decision caching for performance optimization
decision_cache = {}
CACHE_DURATION = 300  # 5 minutes cache for similar prompts

# Enhanced provider state tracking with burst handling
provider_state = {}
for provider_name in RATE_LIMIT_CONFIG.keys():
    provider_state[provider_name] = {
        "last_429": 0,
        "consecutive_failures": 0,
        "requests_this_minute": [],
        "requests_this_hour": [],
        "hour_reset_time": time.time(),
        "burst_tokens": RATE_LIMIT_CONFIG[provider_name]["burst_limit"],
        "last_burst_reset": time.time(),
        "success_rate": 1.0,
        "avg_response_time": 2.0,
        "last_success": time.time(),
    }


def generate_cache_key(prompt: str, symbol: str = "") -> str:
    """Generate cache key for prompt and symbol combination."""
    import hashlib

    content = f"{prompt}_{symbol}".encode("utf-8")
    return hashlib.md5(content).hexdigest()


def can_make_request(provider_name: str) -> bool:
    """
    Check if we can make a request to the specified provider.

    Args:
        provider_name: Name of the AI provider

    Returns:
        bool: True if request can be made, False otherwise
    """
    if provider_name not in provider_state:
        return False

    current_time = time.time()
    state = provider_state[provider_name]
    config = RATE_LIMIT_CONFIG[provider_name]

    # Check if provider is in cooldown from recent failures
    if current_time - state["last_429"] < config["max_cooldown"]:
        return False

    # Check consecutive failures
    if state["consecutive_failures"] >= 3:
        return False

    # Check rate limits
    minute_requests = [
        t for t in state["requests_this_minute"] if current_time - t < 60
    ]
    if len(minute_requests) >= config["requests_per_minute"]:
        return False

    hour_requests = [t for t in state["requests_this_hour"] if current_time - t < 3600]
    if len(hour_requests) >= config["max_requests_per_hour"]:
        return False

    # Check burst tokens
    if state["burst_tokens"] <= 0:
        return False

    return True


def get_cached_decision(cache_key: str) -> Optional[Dict[str, Any]]:
    """Retrieve cached decision if still valid."""
    if cache_key in decision_cache:
        cached_data = decision_cache[cache_key]
        if time.time() - cached_data["timestamp"] < CACHE_DURATION:
            logger.debug(f"Cache hit for key: {cache_key[:8]}...")
            return cached_data["decision"]
        else:
            # Remove expired cache entry
            del decision_cache[cache_key]
    return None


def cache_decision(cache_key: str, decision: Dict[str, Any]) -> None:
    """Cache decision with timestamp."""
    decision_cache[cache_key] = {"decision": decision, "timestamp": time.time()}

    # Clean old cache entries periodically
    if len(decision_cache) > 1000:
        current_time = time.time()
        expired_keys = [
            key
            for key, data in decision_cache.items()
            if current_time - data["timestamp"] > CACHE_DURATION
        ]
        for key in expired_keys:
            del decision_cache[key]


def clean_old_requests(provider_name):
    """Clean old request tracking data and reset burst tokens."""
    current_time = time.time()
    state = provider_state[provider_name]
    config = RATE_LIMIT_CONFIG[provider_name]

    # Clean minute tracking (keep only last 60 seconds)
    state["requests_this_minute"] = [
        req_time
        for req_time in state["requests_this_minute"]
        if current_time - req_time < 60
    ]

    # Clean hour tracking (reset if more than 1 hour passed)
    if current_time - state["hour_reset_time"] > 3600:
        state["requests_this_hour"] = []
        state["hour_reset_time"] = current_time

    # Reset burst tokens every minute
    if current_time - state["last_burst_reset"] > 60:
        state["burst_tokens"] = config["burst_limit"]
        state["last_burst_reset"] = current_time


def is_available(provider_name):
    """Enhanced availability check with burst token support and priority scoring."""
    if provider_name not in provider_state:
        return True

    state = provider_state[provider_name]
    config = RATE_LIMIT_CONFIG[provider_name]
    current_time = time.time()

    # Clean old request tracking
    clean_old_requests(provider_name)

    # Check if we're in cooldown from 429 error
    if state["last_429"] > 0:
        cooldown_duration = min(
            config["base_cooldown"] * (2 ** state["consecutive_failures"]),
            config["max_cooldown"],
        )
        if current_time - state["last_429"] < cooldown_duration:
            return False

    # Check if provider has been failing too much
    if state["success_rate"] < 0.3 and current_time - state["last_success"] > 300:
        logger.debug(
            f"[{provider_name}] Low success rate ({state['success_rate']:.2f}), temporarily disabled"
        )
        return False

    # Allow burst requests if available
    if state["burst_tokens"] > 0:
        return True

    # Check minute rate limit
    if len(state["requests_this_minute"]) >= config["requests_per_minute"]:
        logger.debug(
            f"[{provider_name}] Minute rate limit exceeded ({len(state['requests_this_minute'])}/{config['requests_per_minute']})"
        )
        return False

    # Check hour rate limit
    if len(state["requests_this_hour"]) >= config["max_requests_per_hour"]:
        logger.debug(
            f"[{provider_name}] Hour rate limit exceeded ({len(state['requests_this_hour'])}/{config['max_requests_per_hour']})"
        )
        return False

    return True


def get_provider_priority_score(provider_name: str) -> float:
    """Calculate priority score based on success rate, response time, and configuration."""
    if provider_name not in provider_state:
        return 0.5

    state = provider_state[provider_name]
    config = RATE_LIMIT_CONFIG[provider_name]

    # Base priority from configuration (lower number = higher priority)
    base_score = 1.0 / config["priority"]

    # Adjust for success rate
    success_modifier = state["success_rate"]

    # Adjust for response time (faster = better)
    time_modifier = max(0.1, 1.0 / max(state["avg_response_time"], 0.1))

    # Availability bonus
    availability_bonus = 1.2 if is_available(provider_name) else 0.1

    return base_score * success_modifier * time_modifier * availability_bonus


def record_request(provider_name, response_time: float = 2.0):
    """Record a successful request with enhanced metrics tracking."""
    current_time = time.time()
    state = provider_state[provider_name]

    state["requests_this_minute"].append(current_time)
    state["requests_this_hour"].append(current_time)
    state["consecutive_failures"] = 0
    state["last_success"] = current_time

    # Use burst token if available
    if state["burst_tokens"] > 0:
        state["burst_tokens"] -= 1

    # Update success rate (exponential moving average)
    state["success_rate"] = 0.9 * state["success_rate"] + 0.1 * 1.0

    # Update average response time (exponential moving average)
    state["avg_response_time"] = 0.8 * state["avg_response_time"] + 0.2 * response_time


def update_429(provider_name):
    """Update state after receiving 429 error with enhanced failure tracking."""
    current_time = time.time()
    state = provider_state[provider_name]

    state["last_429"] = current_time
    state["consecutive_failures"] += 1

    # Update success rate (exponential moving average)
    state["success_rate"] = 0.9 * state["success_rate"] + 0.1 * 0.0

    logger.warning(
        f"[{provider_name}] Rate limited. Consecutive failures: {state['consecutive_failures']}"
    )


async def call_provider_async(
    provider_name: str, call_fn, prompt: str
) -> Tuple[str, Optional[Dict[str, Any]]]:
    """Async wrapper for AI provider calls with timing."""
    start_time = time.time()
    try:
        result = call_with_backoff(call_fn, provider_name, prompt)
        response_time = time.time() - start_time
        if result:
            record_request(provider_name, response_time)
        return provider_name, result
    except Exception as e:
        logger.error(f"[{provider_name}] Async call failed: {e}")
        return provider_name, None


def get_ai_decisions_parallel(prompt: str, symbol: str = "") -> Dict[str, Any]:
    """Get AI decisions from multiple providers in parallel for faster processing."""
    # Check cache first
    cache_key = generate_cache_key(prompt, symbol)
    cached_result = get_cached_decision(cache_key)
    if cached_result:
        logger.info(f"🎯 Cache hit for {symbol}, returning cached decision")
        return cached_result

    # Load dynamic weights
    model_weights = load_model_weights()

    # Define providers with priority sorting - Gemini re-enabled with improved rate limiting
    providers = [
        ("Claude", call_claude, model_weights.get("Claude", 0.30)),
        ("OpenAI", call_openai, model_weights.get("OpenAI", 0.30)),
        ("DeepSeek", call_deepseek, model_weights.get("DeepSeek", 0.25)),
        (
            "Gemini",
            call_gemini,
            model_weights.get("Gemini", 0.15),
        ),  # Re-enabled with better handling
    ]

    # Sort providers by priority score for optimal performance
    available_providers = [
        (name, fn, weight) for name, fn, weight in providers if is_available(name)
    ]

    available_providers.sort(
        key=lambda x: get_provider_priority_score(x[0]), reverse=True
    )

    if not available_providers:
        logger.warning("⚠️ No AI providers available, using fallback")
        return {
            "decision": "HOLD",
            "confidence": 0.0,
            "reasoning": "No AI providers available",
            "provider_results": [],
            "timestamp": datetime.now().isoformat(),
            "symbol": symbol,
        }

    # Use ThreadPoolExecutor for parallel processing
    results = []
    with ThreadPoolExecutor(max_workers=min(len(available_providers), 4)) as executor:
        # Enhanced prompt with symbol context
        enhanced_prompt = f"Symbol: {symbol}\n{prompt}\n\nProvide decision as JSON with decision, confidence (0-100), and reason."

        # Submit all tasks
        future_to_provider = {
            executor.submit(call_with_backoff, fn, name, enhanced_prompt): (
                name,
                weight,
            )
            for name, fn, weight in available_providers
        }

        # Collect results as they complete with timeout handling
        try:
            for future in as_completed(future_to_provider, timeout=45):
                name, weight = future_to_provider[future]
                try:
                    result = future.result(timeout=10)
                    if result:
                        validated = validate_response(result)
                        decision = validated.get("decision", "HOLD").upper()
                        confidence = float(validated.get("confidence", 0.0)) / 100.0
                        reason = validated.get(
                            "reason", f"{name} provided no reasoning"
                        )

                        results.append(
                            {
                                "provider": name,
                                "decision": decision,
                                "confidence": confidence,
                                "reason": reason,
                                "weight": weight,
                                "weighted_score": confidence * weight,
                            }
                        )
                        logger.info(
                            f"✅ {name}: {decision} (confidence: {confidence:.2f})"
                        )
                except Exception as e:
                    logger.error(f"❌ {name} failed: {e}")
                    # Update failure metrics
                    if name in provider_state:
                        provider_state[name]["success_rate"] = (
                            0.9 * provider_state[name]["success_rate"] + 0.1 * 0.0
                        )

        except TimeoutError:
            logger.warning(
                "⏰ Some AI providers timed out, proceeding with available results"
            )
            # Cancel remaining futures
            for future in future_to_provider:
                if not future.done():
                    future.cancel()
        except Exception as e:
            logger.error(f"Error in parallel processing: {e}")

    # Process results for consensus
    if not results:
        fallback_result = {
            "decision": "HOLD",
            "confidence": 0.0,
            "consensus_strength": 0.0,
            "reasoning": "All providers failed",
            "provider_results": [],
            "total_providers": 0,
            "timestamp": datetime.now().isoformat(),
            "symbol": symbol,
        }
        cache_decision(cache_key, fallback_result)
        return fallback_result

    # Calculate weighted consensus
    decision_scores = {"BUY": 0.0, "SELL": 0.0, "HOLD": 0.0}
    total_weight = 0.0
    confidence_sum = 0.0

    for result in results:
        decision = result["decision"]
        weighted_score = result["weighted_score"]
        decision_scores[decision] += weighted_score
        confidence_sum += weighted_score
        total_weight += result["weight"]

    # Find winning decision
    winning_decision = max(decision_scores.items(), key=lambda x: x[1])
    final_decision = winning_decision[0]
    decision_strength = winning_decision[1] / total_weight if total_weight > 0 else 0

    # Require minimum consensus (60% agreement)
    consensus_threshold = 0.6
    if decision_strength < consensus_threshold:
        final_decision = "HOLD"
        decision_strength = 0.5
        logger.warning(f"⚠️ No consensus reached for {symbol}, defaulting to HOLD")

    # Calculate overall confidence
    overall_confidence = (
        min(confidence_sum / total_weight, 1.0) if total_weight > 0 else 0.5
    )

    final_result = {
        "decision": final_decision,
        "confidence": overall_confidence,
        "consensus_strength": decision_strength,
        "provider_results": results,
        "total_providers": len(results),
        "reasoning": f"Consensus: {final_decision} with {decision_strength:.2f} strength from {len(results)} providers",
        "timestamp": datetime.now().isoformat(),
        "symbol": symbol,
    }

    # Cache the result
    cache_decision(cache_key, final_result)

    return final_result


def fallback_decision(symbol: str, reason: str) -> Dict[str, Any]:
    """Generate fallback decision when all providers fail."""
    return {
        "decision": "HOLD",
        "confidence": 0.0,
        "consensus_strength": 0.0,
        "provider_results": [],
        "total_providers": 0,
        "reasoning": reason,
        "timestamp": datetime.now().isoformat(),
        "symbol": symbol,
    }


def extract_decision_from_text(text_response):
    """Extract trading decision from plain text response."""
    if not isinstance(text_response, str):
        return None

    text_upper = text_response.upper()

    # Look for explicit decisions at the start of lines
    lines = text_response.split("\n")
    for line in lines[:5]:  # Check first 5 lines
        line_upper = line.strip().upper()
        if line_upper.startswith("BUY"):
            return {
                "decision": "BUY",
                "confidence": 0.7,
                "reason": "AI text analysis suggests buying",
            }
        elif line_upper.startswith("SELL"):
            return {
                "decision": "SELL",
                "confidence": 0.7,
                "reason": "AI text analysis suggests selling",
            }
        elif line_upper.startswith("HOLD"):
            return {
                "decision": "HOLD",
                "confidence": 0.7,
                "reason": "AI text analysis suggests holding",
            }

    # Count decision keywords in the text
    buy_count = (
        text_upper.count("BUY")
        + text_upper.count("BULLISH")
        + text_upper.count("POSITIVE")
    )
    sell_count = (
        text_upper.count("SELL")
        + text_upper.count("BEARISH")
        + text_upper.count("NEGATIVE")
    )
    hold_count = (
        text_upper.count("HOLD")
        + text_upper.count("NEUTRAL")
        + text_upper.count("WAIT")
    )

    # Determine decision based on keyword frequency
    if buy_count > sell_count and buy_count > hold_count:
        return {
            "decision": "BUY",
            "confidence": 0.6,
            "reason": "Text analysis indicates bullish sentiment",
        }
    elif sell_count > buy_count and sell_count > hold_count:
        return {
            "decision": "SELL",
            "confidence": 0.6,
            "reason": "Text analysis indicates bearish sentiment",
        }
    else:
        return {
            "decision": "HOLD",
            "confidence": 0.5,
            "reason": "Text analysis indicates neutral sentiment",
        }


def call_with_backoff(call_fn, provider_name, prompt, max_retries=1):
    """Enhanced call function with improved rate limiting and backoff."""
    config = RATE_LIMIT_CONFIG[provider_name]
    base_delay = config["base_cooldown"]

    for attempt in range(1, max_retries + 1):
        try:
            logger.debug(
                f"[{provider_name}] Making API call (attempt {attempt}/{max_retries})"
            )
            response = call_fn(prompt)

            # Check if the response indicates an error
            if isinstance(response, dict) and response.get("decision") == "ERROR":
                logger.error(
                    f"[{provider_name}] API returned error response: {response.get('reason', 'Unknown error')}"
                )
                provider_state[provider_name]["consecutive_failures"] += 1

                # If it's an auth error, don't retry
                if "401" in str(response.get("reason", "")) or "Unauthorized" in str(
                    response.get("reason", "")
                ):
                    logger.error(
                        f"[{provider_name}] Authentication error, skipping retries"
                    )
                    return None

                # For other errors, continue to retry logic
                if attempt < max_retries:
                    delay = base_delay * attempt
                    logger.info(
                        f"[{provider_name}] Retrying in {delay}s due to error response..."
                    )
                    time.sleep(delay)
                    continue
                else:
                    logger.error(
                        f"[{provider_name}] Max retries exceeded with error responses"
                    )
                    return None

            # Record successful request
            record_request(provider_name)
            provider_state[provider_name]["consecutive_failures"] = 0

            # Track API usage for cost monitoring
            if COST_TRACKING_AVAILABLE:
                # Estimate tokens based on prompt length (rough approximation)
                estimated_tokens = len(prompt.split()) * 1.3  # Average tokens per word
                record_api_usage(
                    service=provider_name.lower(),
                    endpoint="/chat/completions",
                    tokens=int(estimated_tokens),
                )

            # Attempt to parse JSON if response is a string, for better validation
            if isinstance(response, str):
                try:
                    parsed_response = json.loads(response)
                    if isinstance(parsed_response, dict):
                        response = parsed_response
                except json.JSONDecodeError:
                    # If it's not JSON, try to extract decision from text
                    extracted = extract_decision_from_text(response)
                    if extracted:
                        response = extracted

            logger.info(f"[{provider_name}] ✅ Successful API call")
            return response

        except requests.exceptions.RequestException as e:
            error_str = str(e).lower()

            if (
                "429" in str(e)
                or "rate limit" in error_str
                or "too many requests" in error_str
            ):
                logger.warning(
                    f"[{provider_name}] Rate limited (attempt {attempt}/{max_retries}): {e}"
                )
                update_429(provider_name)

                if attempt < max_retries:
                    # Calculate exponential backoff with jitter
                    delay = base_delay * (2 ** (attempt - 1)) + (
                        time.time() % 1
                    )  # Add jitter
                    delay = min(delay, config["max_cooldown"] / 4)  # Cap delay
                    logger.info(
                        f"[{provider_name}] Waiting {delay:.1f}s before retry..."
                    )
                    time.sleep(delay)
                else:
                    logger.error(
                        f"[{provider_name}] Max retries exceeded for rate limiting"
                    )
                    break

            elif "unauthorized" in error_str or "401" in str(e):
                logger.error(f"[{provider_name}] Authentication error: {e}")
                # Don't retry auth errors
                break

            elif "timeout" in error_str or "connection" in error_str:
                logger.warning(
                    f"[{provider_name}] Connection error (attempt {attempt}/{max_retries}): {e}"
                )
                if attempt < max_retries:
                    delay = base_delay * attempt
                    logger.info(f"[{provider_name}] Retrying in {delay}s...")
                    time.sleep(delay)
                else:
                    break
            else:
                logger.error(f"[{provider_name}] Network error: {e}")
                break

        except json.JSONDecodeError as e:
            logger.error(f"[{provider_name}] JSON decoding error: {e}")
            break  # No point in retrying if response is consistently malformed

        except Exception as e:
            logger.error(
                f"[{provider_name}] Unexpected error (attempt {attempt}/{max_retries}): {e}"
            )
            if attempt < max_retries:
                delay = base_delay * attempt
                time.sleep(delay)
            else:
                break

    logger.error(f"[{provider_name}] ❌ All retry attempts failed")
    return None


def validate_response(response):
    """Ensure all AI provider responses are properly validated and prevent crashes due to incorrect data structures."""
    if isinstance(response, dict):
        return response
    if response is None:
        logger.warning("AI response is None. Returning empty dict.")
        return {}
    if isinstance(response, str):
        try:
            response = json.loads(response)
            if isinstance(response, dict):
                return response
            else:
                logger.warning(
                    "AI response was a string but not a dict after json.loads. Returning empty dict."
                )
        except json.JSONDecodeError:
            logger.warning(
                "AI response string could not be decoded as JSON. Returning empty dict."
            )
    if isinstance(response, list):
        logger.warning("AI response is a list, expected dict. Returning empty dict.")
    else:
        logger.warning(
            f"Unexpected AI response type: {type(response)}. Returning empty dict."
        )
    return {}


def get_ai_decision(prompt):
    """Main function to get AI decision from multiple providers with weighted aggregation."""
    # Load weights dynamically
    model_weights = load_model_weights()

    providers = [
        ("Claude", call_claude, model_weights.get("Claude", 0.30)),
        ("OpenAI", call_openai, model_weights.get("OpenAI", 0.30)),
        ("DeepSeek", call_deepseek, model_weights.get("DeepSeek", 0.25)),
        (
            "Gemini",
            call_gemini,
            model_weights.get("Gemini", 0.15),
        ),  # Re-enabled with better handling
    ]

    results = []
    total_weight = 0
    decision_weights = {"BUY": 0.0, "SELL": 0.0, "HOLD": 0.0}
    total_confidence = 0.0

    for name, fn, weight in providers:
        if is_available(name):
            logger.info(f"⚙️ Trying provider: {name}")
            result = call_with_backoff(fn, name, prompt)
            validated = validate_response(result)

            decision = validated.get("decision", "HOLD").upper()
            confidence = float(validated.get("confidence", 0.0))
            reason = validated.get("reason", f"{name} did not provide reasoning.")

            results.append(
                {
                    "provider": name,
                    "decision": decision,
                    "confidence": confidence,
                    "reason": reason,
                    "weight": weight,
                }
            )

            if decision not in decision_weights:
                decision_weights[decision] = 0
            decision_weights[decision] += float(weight)
            total_confidence += confidence * weight
            total_weight += weight
        else:
            logger.warning(f"[{name}] ❌ Skipped due to cooldown.")

    if not results:
        logger.error("🚫 All AI providers failed or rate-limited. Defaulting to HOLD.")
        try:
            from main import telegram_app_instance  # type: ignore
        except ImportError:
            telegram_app_instance = None
            logger.warning("Could not import telegram_app_instance from backend.main.")
        if telegram_app_instance:
            # Import notify_error here to avoid circular import issues
            try:
                from main import notify_error  # type: ignore

                asyncio.create_task(
                    notify_error(
                        telegram_app_instance,
                        "All AI providers failed or rate-limited. Check API keys and service status.",
                    )
                )
            except ImportError:
                logger.warning("Could not import notify_error from backend.main.")
        return {
            "provider": "None",
            "response": {
                "decision": "HOLD",
                "confidence": 0.0,
                "reason": "All providers rate-limited or unavailable. Defaulting to HOLD.",
            },
            "breakdown": [],
        }

    # Select the decision with highest cumulative weight
    if total_weight == 0:
        final_decision = "HOLD"
        weighted_confidence = 0.0
    else:
        final_decision = max(decision_weights.items(), key=lambda x: x[1])[0]
        weighted_confidence = round(total_confidence / total_weight, 2)

    return {
        "provider": "Multi-AI Weighted",
        "response": {
            "decision": final_decision,
            "confidence": weighted_confidence,
            "reason": "Decision derived from weighted aggregation of multiple AI providers.",
        },
        "breakdown": results,
    }


def get_ai_decision_with_consensus(prompt: str, symbol: str) -> Dict[str, Any]:
    """Enhanced AI decision with proper consensus logic and confidence weighting"""

    # Load dynamic weights
    model_weights = load_model_weights()

    # Define providers with actual implementations - all 4 AI models re-enabled
    providers = [
        ("Claude", call_claude, model_weights.get("Claude", 0.30)),
        ("OpenAI", call_openai, model_weights.get("OpenAI", 0.30)),
        ("DeepSeek", call_deepseek, model_weights.get("DeepSeek", 0.25)),
        (
            "Gemini",
            call_gemini,
            model_weights.get("Gemini", 0.15),
        ),  # Re-enabled with improved rate limiting
    ]

    results = []
    decision_scores = {"BUY": 0.0, "SELL": 0.0, "HOLD": 0.0}
    total_weight = 0.0
    confidence_sum = 0.0

    # Collect decisions from all available providers
    for name, fn, weight in providers:
        if is_available(name):
            logger.info(f"🧠 Querying {name} for {symbol}")

            # Enhanced prompt with symbol context
            enhanced_prompt = f"Symbol: {symbol}\n{prompt}\n\nProvide decision as JSON with decision, confidence (0-100), and reason."

            result = call_with_backoff(fn, name, enhanced_prompt)
            validated = validate_response(result)

            decision = validated.get("decision", "HOLD").upper()
            confidence = (
                float(validated.get("confidence", 0.0)) / 100.0
            )  # Normalize to 0-1
            reason = validated.get("reason", f"{name} provided no reasoning")

            # Apply confidence-weighted scoring
            weighted_confidence = confidence * weight
            decision_scores[decision] += weighted_confidence

            results.append(
                {
                    "provider": name,
                    "decision": decision,
                    "confidence": confidence,
                    "reason": reason,
                    "weight": weight,
                    "weighted_score": weighted_confidence,
                }
            )

            confidence_sum += weighted_confidence
            total_weight += weight

    # Determine consensus with minimum threshold
    if total_weight == 0:
        return fallback_decision(symbol, "No AI providers available")

    # Find winning decision
    winning_decision = max(decision_scores.items(), key=lambda x: x[1])
    final_decision = winning_decision[0]
    decision_strength = winning_decision[1] / total_weight

    # Require minimum consensus (60% agreement)
    consensus_threshold = 0.6
    if decision_strength < consensus_threshold:
        final_decision = "HOLD"
        decision_strength = 0.5
        logger.warning(f"⚠️ No consensus reached for {symbol}, defaulting to HOLD")

    # Calculate overall confidence
    overall_confidence = (
        min(confidence_sum / total_weight, 1.0) if total_weight > 0 else 0.5
    )

    return {
        "decision": final_decision,
        "confidence": overall_confidence,
        "consensus_strength": decision_strength,
        "provider_results": results,
        "total_providers": len([r for r in results if r["confidence"] > 0]),
        "reasoning": f"Consensus: {final_decision} with {decision_strength:.2f} strength from {len(results)} providers",
        "timestamp": datetime.now().isoformat(),
        "symbol": symbol,
    }


def make_ai_request(
    prompt: str, provider: str = "auto", symbol: str = ""
) -> Dict[str, Any]:
    """
    Make a single AI request to a specific provider or auto-select best provider.

    Args:
        prompt: The prompt to send to the AI
        provider: Specific provider ("OpenAI", "Claude", "Gemini", "DeepSeek") or "auto"
        symbol: Trading symbol for context

    Returns:
        Dict with decision, confidence, reason, and metadata
    """
    try:
        if provider == "auto":
            # Auto-select best available provider
            available_providers = []
            for prov_name in RATE_LIMIT_CONFIG.keys():
                if can_make_request(prov_name):
                    available_providers.append(
                        (prov_name, provider_state[prov_name]["success_rate"])
                    )

            if not available_providers:
                return {
                    "decision": "HOLD",
                    "confidence": 30.0,
                    "reason": "No AI providers available",
                    "provider": "fallback",
                }

            # Select provider with highest success rate
            provider = max(available_providers, key=lambda x: x[1])[0]

        # Make request to specific provider
        if provider == "OpenAI":
            result = call_openai(prompt)
        elif provider == "Claude":
            result = call_claude(prompt)
        elif provider == "Gemini":
            result = call_gemini(prompt)
        elif provider == "DeepSeek":
            result = call_deepseek(prompt)
        else:
            return {
                "decision": "HOLD",
                "confidence": 30.0,
                "reason": f"Unknown provider: {provider}",
                "provider": "fallback",
            }

        # Add metadata
        result["provider"] = provider
        result["timestamp"] = datetime.now().isoformat()
        result["symbol"] = symbol

        return result

    except Exception as e:
        logging.error(f"AI request failed for {provider}: {e}")
        return {
            "decision": "HOLD",
            "confidence": 25.0,
            "reason": f"AI request failed: {str(e)[:100]}",
            "provider": provider,
            "error": True,
        }
