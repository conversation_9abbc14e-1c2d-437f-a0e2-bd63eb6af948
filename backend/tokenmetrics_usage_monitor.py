#!/usr/bin/env python3
"""
📊 TokenMetrics API Usage Monitor
Ensures we stay under 20,000 monthly API calls with smart rate limiting
"""

import json
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
import os
from pathlib import Path

logger = logging.getLogger(__name__)


class TokenMetricsUsageMonitor:
    """
    Comprehensive API usage monitoring for TokenMetrics
    Prevents exceeding 20,000 monthly API calls
    """

    def __init__(self):
        self.usage_file = Path("data/tokenmetrics_usage.json")
        self.usage_file.parent.mkdir(exist_ok=True)

        # Monthly limits (VERY conservative for $100/month membership)
        self.monthly_limit = 15000  # 15k limit (5k buffer from 20k)
        self.daily_limit = 500  # ~15k/30 days
        self.hourly_limit = 20  # ~500/24 hours
        self.minute_limit = 3  # Max 3 minutes between calls

        # Cost tracking ($100/month membership)
        self.membership_cost = 100  # $100/month
        self.cost_per_call = (
            self.membership_cost / self.monthly_limit
        )  # ~$0.0067 per call

        # Priority endpoints (most valuable for $100/month)
        self.priority_endpoints = {
            "high": ["/trading-signals", "/sentiments", "/quantmetrics"],
            "medium": ["/scenario-analysis", "/correlation", "/resistance-support"],
            "low": ["/investor-grades", "/market-metrics", "/tokens"],
        }

        # Load existing usage data
        self.usage_data = self._load_usage_data()

        # Initialize current month if needed
        current_month = datetime.now().strftime("%Y-%m")
        if current_month not in self.usage_data:
            self.usage_data[current_month] = {
                "total_calls": 0,
                "daily_calls": {},
                "hourly_calls": {},
                "endpoint_usage": {},
                "last_call_time": 0,
                "month_start": datetime.now().replace(day=1).isoformat(),
                "warnings_sent": [],
                "cost_spent": 0.0,
                "value_generated": 0.0,
            }

    def _load_usage_data(self) -> Dict[str, Any]:
        """Load usage data from file"""
        try:
            if self.usage_file.exists():
                with open(self.usage_file, "r") as f:
                    return json.load(f)
            return {}
        except Exception as e:
            logger.error(f"Error loading usage data: {e}")
            return {}

    def _save_usage_data(self):
        """Save usage data to file"""
        try:
            with open(self.usage_file, "w") as f:
                json.dump(self.usage_data, f, indent=2)
        except Exception as e:
            logger.error(f"Error saving usage data: {e}")

    def can_make_call(
        self, endpoint: str = "unknown", priority: str = "medium"
    ) -> tuple[bool, str]:
        """
        Check if we can make an API call without exceeding limits
        Priority-based checking for $100/month value optimization
        Returns (can_call, reason)
        """
        current_month = datetime.now().strftime("%Y-%m")
        current_day = datetime.now().strftime("%Y-%m-%d")
        current_hour = datetime.now().strftime("%Y-%m-%d-%H")
        current_time = time.time()

        month_data = self.usage_data.get(current_month, {})

        # Check monthly limit
        monthly_calls = month_data.get("total_calls", 0)
        if monthly_calls >= self.monthly_limit:
            return False, f"Monthly limit reached: {monthly_calls}/{self.monthly_limit}"

        # Check daily limit
        daily_calls = month_data.get("daily_calls", {}).get(current_day, 0)
        if daily_calls >= self.daily_limit:
            return False, f"Daily limit reached: {daily_calls}/{self.daily_limit}"

        # Check hourly limit
        hourly_calls = month_data.get("hourly_calls", {}).get(current_hour, 0)
        if hourly_calls >= self.hourly_limit:
            return False, f"Hourly limit reached: {hourly_calls}/{self.hourly_limit}"

        # Check minute rate limiting (3 minutes for $100/month optimization)
        last_call_time = month_data.get("last_call_time", 0)
        min_interval = 180  # 3 minutes between calls

        # Adjust interval based on priority
        if priority == "high":
            min_interval = 120  # 2 minutes for high priority
        elif priority == "low":
            min_interval = 300  # 5 minutes for low priority

        if current_time - last_call_time < min_interval:
            wait_time = min_interval - (current_time - last_call_time)
            return (
                False,
                f"Rate limit ({priority} priority): wait {wait_time:.1f} seconds",
            )

        # Check if approaching limits (warnings)
        if monthly_calls >= self.monthly_limit * 0.8:  # 80% of monthly limit
            logger.warning(
                f"⚠️ TokenMetrics usage at 80%: {monthly_calls}/{self.monthly_limit}"
            )

        return True, "OK"

    def record_api_call(
        self,
        endpoint: str,
        success: bool = True,
        response_size: int = 0,
        data_points: int = 0,
    ):
        """Record an API call in usage statistics with cost and value tracking"""
        current_month = datetime.now().strftime("%Y-%m")
        current_day = datetime.now().strftime("%Y-%m-%d")
        current_hour = datetime.now().strftime("%Y-%m-%d-%H")
        current_time = time.time()

        # Ensure current month exists
        if current_month not in self.usage_data:
            self.usage_data[current_month] = {
                "total_calls": 0,
                "daily_calls": {},
                "hourly_calls": {},
                "endpoint_usage": {},
                "last_call_time": 0,
                "month_start": datetime.now().replace(day=1).isoformat(),
                "warnings_sent": [],
                "cost_spent": 0.0,
                "value_generated": 0.0,
            }

        month_data = self.usage_data[current_month]

        # Update counters
        month_data["total_calls"] += 1
        month_data["daily_calls"][current_day] = (
            month_data["daily_calls"].get(current_day, 0) + 1
        )
        month_data["hourly_calls"][current_hour] = (
            month_data["hourly_calls"].get(current_hour, 0) + 1
        )
        month_data["last_call_time"] = current_time

        # Update cost tracking
        call_cost = self.cost_per_call
        month_data["cost_spent"] = month_data.get("cost_spent", 0.0) + call_cost

        # Calculate value generated (data points * quality multiplier)
        value_multiplier = 1.0
        if data_points > 0:
            value_multiplier = min(
                3.0, data_points / 10
            )  # Max 3x value for 30+ data points

        month_data["value_generated"] = month_data.get("value_generated", 0.0) + (
            call_cost * value_multiplier
        )

        # Update endpoint usage
        if endpoint not in month_data["endpoint_usage"]:
            month_data["endpoint_usage"][endpoint] = {
                "calls": 0,
                "success_calls": 0,
                "failed_calls": 0,
                "total_response_size": 0,
            }

        endpoint_data = month_data["endpoint_usage"][endpoint]
        endpoint_data["calls"] += 1
        endpoint_data["total_response_size"] += response_size

        if success:
            endpoint_data["success_calls"] += 1
        else:
            endpoint_data["failed_calls"] += 1

        # Save updated data
        self._save_usage_data()

        # Log usage info
        logger.info(
            f"📊 TokenMetrics API call recorded: {endpoint} "
            f"(Monthly: {month_data['total_calls']}/{self.monthly_limit})"
        )

        # Check for warnings
        self._check_usage_warnings(current_month)

    def _check_usage_warnings(self, current_month: str):
        """Check and send usage warnings"""
        month_data = self.usage_data[current_month]
        total_calls = month_data["total_calls"]
        warnings_sent = month_data.get("warnings_sent", [])

        # 50% warning
        if total_calls >= self.monthly_limit * 0.5 and "50%" not in warnings_sent:
            logger.warning(
                f"⚠️ TokenMetrics usage at 50%: {total_calls}/{self.monthly_limit}"
            )
            warnings_sent.append("50%")

        # 75% warning
        if total_calls >= self.monthly_limit * 0.75 and "75%" not in warnings_sent:
            logger.warning(
                f"🚨 TokenMetrics usage at 75%: {total_calls}/{self.monthly_limit}"
            )
            warnings_sent.append("75%")

        # 90% critical warning
        if total_calls >= self.monthly_limit * 0.9 and "90%" not in warnings_sent:
            logger.critical(
                f"🔴 CRITICAL: TokenMetrics usage at 90%: {total_calls}/{self.monthly_limit}"
            )
            warnings_sent.append("90%")

        month_data["warnings_sent"] = warnings_sent

    def get_usage_stats(self) -> Dict[str, Any]:
        """Get comprehensive usage statistics"""
        current_month = datetime.now().strftime("%Y-%m")
        current_day = datetime.now().strftime("%Y-%m-%d")

        month_data = self.usage_data.get(current_month, {})

        stats = {
            "current_month": current_month,
            "monthly_usage": {
                "calls_made": month_data.get("total_calls", 0),
                "monthly_limit": self.monthly_limit,
                "percentage_used": (
                    month_data.get("total_calls", 0) / self.monthly_limit
                )
                * 100,
                "calls_remaining": self.monthly_limit
                - month_data.get("total_calls", 0),
                "cost_spent": month_data.get("cost_spent", 0.0),
                "value_generated": month_data.get("value_generated", 0.0),
                "budget_remaining": 100.0 - month_data.get("cost_spent", 0.0),
                "roi": (
                    (
                        month_data.get("value_generated", 0.0)
                        / max(month_data.get("cost_spent", 0.01), 0.01)
                    )
                    if month_data.get("cost_spent", 0) > 0
                    else 0.0
                ),
            },
            "daily_usage": {
                "today_calls": month_data.get("daily_calls", {}).get(current_day, 0),
                "daily_limit": self.daily_limit,
                "daily_percentage": (
                    month_data.get("daily_calls", {}).get(current_day, 0)
                    / self.daily_limit
                )
                * 100,
            },
            "endpoint_breakdown": month_data.get("endpoint_usage", {}),
            "rate_limiting": {
                "hourly_limit": self.hourly_limit,
                "minute_limit": self.minute_limit,
                "last_call": month_data.get("last_call_time", 0),
            },
            "projections": self._calculate_projections(month_data),
            "warnings": month_data.get("warnings_sent", []),
        }

        return stats

    def _calculate_projections(self, month_data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate usage projections for the month"""
        try:
            total_calls = month_data.get("total_calls", 0)
            month_start = datetime.fromisoformat(
                month_data.get("month_start", datetime.now().isoformat())
            )
            days_elapsed = (datetime.now() - month_start).days + 1
            days_in_month = 30  # Conservative estimate

            if days_elapsed > 0:
                daily_average = total_calls / days_elapsed
                projected_monthly = daily_average * days_in_month

                return {
                    "daily_average": round(daily_average, 2),
                    "projected_monthly_calls": round(projected_monthly),
                    "projected_percentage": round(
                        (projected_monthly / self.monthly_limit) * 100, 1
                    ),
                    "days_elapsed": days_elapsed,
                    "on_track": projected_monthly <= self.monthly_limit,
                }

            return {"error": "Insufficient data for projections"}

        except Exception as e:
            logger.error(f"Error calculating projections: {e}")
            return {"error": str(e)}

    def reset_monthly_usage(self):
        """Reset usage for new month (called automatically)"""
        current_month = datetime.now().strftime("%Y-%m")
        if current_month not in self.usage_data:
            self.usage_data[current_month] = {
                "total_calls": 0,
                "daily_calls": {},
                "hourly_calls": {},
                "endpoint_usage": {},
                "last_call_time": 0,
                "month_start": datetime.now().replace(day=1).isoformat(),
                "warnings_sent": [],
            }
            self._save_usage_data()
            logger.info(f"🔄 TokenMetrics usage reset for new month: {current_month}")

    def get_recommended_call_frequency(self) -> Dict[str, Any]:
        """Get recommended API call frequency to stay within limits"""
        stats = self.get_usage_stats()
        calls_remaining = stats["monthly_usage"]["calls_remaining"]

        # Calculate days remaining in month
        now = datetime.now()
        next_month = (now.replace(day=28) + timedelta(days=4)).replace(day=1)
        days_remaining = (next_month - now).days

        if days_remaining > 0:
            recommended_daily = calls_remaining / days_remaining
            recommended_hourly = recommended_daily / 24

            return {
                "days_remaining": days_remaining,
                "calls_remaining": calls_remaining,
                "recommended_daily": round(recommended_daily, 1),
                "recommended_hourly": round(recommended_hourly, 1),
                "safe_interval_minutes": (
                    round(60 / recommended_hourly) if recommended_hourly > 0 else 60
                ),
            }

        return {"error": "Month ending soon"}


# Global instance
usage_monitor = TokenMetricsUsageMonitor()


def check_api_limit(endpoint: str = "unknown") -> tuple[bool, str]:
    """Check if API call is allowed"""
    return usage_monitor.can_make_call(endpoint)


def record_api_usage(endpoint: str, success: bool = True, response_size: int = 0):
    """Record API usage"""
    usage_monitor.record_api_call(endpoint, success, response_size)


def get_usage_summary() -> Dict[str, Any]:
    """Get usage summary"""
    return usage_monitor.get_usage_stats()
