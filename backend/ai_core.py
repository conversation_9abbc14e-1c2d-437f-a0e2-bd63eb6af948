import asyncio
import logging
import json
import numpy as np
from typing import Dict, Any, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

# Global AI decision engine instance
ai_engine = None


class ProfitableAIDecisionEngine:
    """
    Enhanced AI decision engine designed for profitability
    """

    def __init__(self):
        pass

    async def analyze_token_with_200_data_points(self, symbol: str, data_points: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze a token using 200+ data points and return a comprehensive AI decision.
        """
        # Use the same logic as get_profitable_decision but with provided data_points
        try:
            logger.info(f"🧠 Comprehensive analysis for {symbol} using provided data points")

            # 1. Technical analysis (40% weight)
            technical_data = self._analyze_technical_signals(symbol)
            technical_score = technical_data["score"]

            # 2. Enhanced sentiment analysis (25% weight)
            try:
                from sentiment_engine import get_combined_sentiment

                sentiment_result = get_combined_sentiment(symbol)
                sentiment_score = sentiment_result.get("combined_score", 0.5)
                sentiment_confidence = sentiment_result.get("confidence", 0.5)
            except Exception as e:
                logger.warning(f"Sentiment analysis failed for {symbol}: {e}")
                sentiment_score = 0.5
                sentiment_confidence = 0.1

            # 3. Market data analysis (20% weight)
            market_score = await self._analyze_market_conditions(symbol, data_points)

            # 4. Volume and liquidity analysis (10% weight)
            volume_score = self._analyze_volume_patterns(data_points)

            # 5. TokenMetrics integration (5% weight)
            tokenmetrics_score = await self._get_tokenmetrics_score(symbol)

            # Weighted scoring
            weights = {
                "technical": 0.40,
                "sentiment": 0.25,
                "market": 0.20,
                "volume": 0.10,
                "tokenmetrics": 0.05,
            }

            combined_score = (
                technical_score * weights["technical"]
                + sentiment_score * weights["sentiment"]
                + market_score * weights["market"]
                + volume_score * weights["volume"]
                + tokenmetrics_score * weights["tokenmetrics"]
            )

            # Decision logic
            if combined_score >= 0.65:
                decision = "BUY"
                confidence = min(0.85, combined_score * 1.2)
                reasoning = f"Strong bullish signals detected. {technical_data['reasoning']} | Sentiment: {sentiment_score:.2f}"
            elif combined_score <= 0.35:
                decision = "SELL"
                confidence = min(0.85, (1 - combined_score) * 1.2)
                reasoning = f"Strong bearish signals detected. {technical_data['reasoning']} | Sentiment: {sentiment_score:.2f}"
            else:
                decision = "HOLD"
                confidence = 0.6
                reasoning = f"Mixed signals, holding position. {technical_data['reasoning']} | Sentiment: {sentiment_score:.2f}"

            if technical_data.get("rsi", 50) < 25 or technical_data.get("rsi", 50) > 75:
                confidence = min(0.9, confidence * 1.1)
                reasoning += " | Extreme RSI levels detected"

            result = {
                "decision": decision,
                "confidence": round(confidence, 2),
                "reasoning": reasoning,
                "timestamp": datetime.utcnow().isoformat(),
                "technical_score": technical_score,
                "sentiment_score": sentiment_score,
                "market_score": market_score,
                "volume_score": volume_score,
                "tokenmetrics_score": tokenmetrics_score,
                "combined_score": combined_score,
                "data_points_used": len(data_points),
                "sentiment_confidence": sentiment_confidence,
                "analysis_breakdown": {
                    "technical": f"{technical_score:.3f} (40%)",
                    "sentiment": f"{sentiment_score:.3f} (25%)",
                    "market": f"{market_score:.3f} (20%)",
                    "volume": f"{volume_score:.3f} (10%)",
                    "tokenmetrics": f"{tokenmetrics_score:.3f} (5%)",
                },
            }

            logger.info(
                f"✅ {symbol} comprehensive decision: {decision} (confidence: {confidence:.2f})"
            )
            return result

        except Exception as e:
            logger.error(f"❌ Error in comprehensive decision for {symbol}: {e}")
            return {
                "decision": "HOLD",
                "confidence": 0.5,
                "reasoning": f"Analysis error: {str(e)}",
                "timestamp": datetime.utcnow().isoformat(),
                "data_points_used": len(data_points),
            }

    def _calculate_rsi(self, prices: list, period: int = 14) -> float:
        """Calculate RSI indicator"""
        if len(prices) < period + 1:
            return 50.0

        deltas = np.diff(prices)
        gains = np.where(deltas > 0, deltas, 0)
        losses = np.where(deltas < 0, -deltas, 0)

        avg_gain = np.mean(gains[:period])
        avg_loss = np.mean(losses[:period])

        if avg_loss == 0:
            return 100.0

        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        return float(rsi)

    def _analyze_technical_signals(self, symbol: str) -> Dict[str, Any]:
        """Analyze technical indicators for trading signals"""
        try:
            from kucoin_data import fetch_kucoin_data

            # Get candlestick data
            candles_1h = fetch_kucoin_data(symbol, "1hour", 50)
            candles_4h = fetch_kucoin_data(symbol, "4hour", 30)

            if not candles_1h or not candles_4h:
                return {"score": 0.5, "signals": [], "reasoning": "Insufficient data"}

            signals = []
            scores = []

            # 1. RSI Analysis
            prices_1h = [float(c.get("close", 0)) for c in candles_1h]
            rsi = self._calculate_rsi(prices_1h)

            if rsi < 30:
                signals.append("RSI oversold (bullish)")
                scores.append(0.8)
            elif rsi > 70:
                signals.append("RSI overbought (bearish)")
                scores.append(0.2)
            else:
                signals.append(f"RSI neutral ({rsi:.1f})")
                scores.append(0.5)

            # 2. Moving Average Analysis
            ma_20 = np.mean(prices_1h[-20:])
            ma_50 = np.mean(prices_1h[-50:]) if len(prices_1h) >= 50 else ma_20
            current_price = prices_1h[-1]

            if current_price > ma_20 > ma_50:
                signals.append("Price above MAs (bullish)")
                scores.append(0.75)
            elif current_price < ma_20 < ma_50:
                signals.append("Price below MAs (bearish)")
                scores.append(0.25)
            else:
                signals.append("Mixed MA signals")
                scores.append(0.5)

            # 3. Volume Analysis
            volumes = [float(c.get("volume", 0)) for c in candles_1h[-10:]]
            avg_volume = np.mean(volumes[:-1])
            current_volume = volumes[-1]

            if current_volume > avg_volume * 1.5:
                signals.append("High volume (strong signal)")
                scores.append(0.7)
            elif current_volume < avg_volume * 0.5:
                signals.append("Low volume (weak signal)")
                scores.append(0.4)
            else:
                signals.append("Normal volume")
                scores.append(0.5)

            # 4. Price Momentum
            price_change_1h = (prices_1h[-1] - prices_1h[-2]) / prices_1h[-2] * 100
            price_change_4h = (
                (
                    float(candles_4h[-1].get("close", 0))
                    - float(candles_4h[-2].get("close", 0))
                )
                / float(candles_4h[-2].get("close", 1))
                * 100
            )

            if price_change_1h > 2 and price_change_4h > 1:
                signals.append("Strong upward momentum")
                scores.append(0.8)
            elif price_change_1h < -2 and price_change_4h < -1:
                signals.append("Strong downward momentum")
                scores.append(0.2)
            else:
                signals.append(
                    f"Momentum: 1h={price_change_1h:.1f}%, 4h={price_change_4h:.1f}%"
                )
                scores.append(0.5)

            overall_score = np.mean(scores)

            return {
                "score": overall_score,
                "signals": signals,
                "reasoning": " | ".join(signals),
                "rsi": rsi,
                "price_change_1h": price_change_1h,
                "price_change_4h": price_change_4h,
            }

        except Exception as e:
            logger.error(f"Technical analysis error for {symbol}: {e}")
            return {
                "score": 0.5,
                "signals": [],
                "reasoning": f"Technical analysis failed: {e}",
            }

    async def get_profitable_decision(self, symbol: str) -> Dict[str, Any]:
        """Get AI decision with comprehensive data analysis and proper reasoning"""
        try:
            logger.info(
                f"🧠 Comprehensive analysis for {symbol} using 200+ data points"
            )

            # 📊 DATA COLLECTION PHASE - Gather comprehensive data
            data_points = await self._collect_comprehensive_data(symbol)
            logger.info(f"📈 Collected {len(data_points)} data points for {symbol}")

            # 1. Technical analysis (40% weight)
            technical_data = self._analyze_technical_signals(symbol)
            technical_score = technical_data["score"]

            # 2. Enhanced sentiment analysis (25% weight)
            try:
                from sentiment_engine import get_combined_sentiment

                sentiment_result = get_combined_sentiment(symbol)
                sentiment_score = sentiment_result.get("combined_score", 0.5)
                sentiment_confidence = sentiment_result.get("confidence", 0.5)
            except Exception as e:
                logger.warning(f"Sentiment analysis failed for {symbol}: {e}")
                sentiment_score = 0.5
                sentiment_confidence = 0.1

            # 3. Market data analysis (20% weight)
            market_score = await self._analyze_market_conditions(symbol, data_points)

            # 4. Volume and liquidity analysis (10% weight)
            volume_score = self._analyze_volume_patterns(data_points)

            # 5. TokenMetrics integration (5% weight)
            tokenmetrics_score = await self._get_tokenmetrics_score(symbol)

            # 🧮 WEIGHTED SCORING with comprehensive data
            weights = {
                "technical": 0.40,
                "sentiment": 0.25,
                "market": 0.20,
                "volume": 0.10,
                "tokenmetrics": 0.05,
            }

            combined_score = (
                technical_score * weights["technical"]
                + sentiment_score * weights["sentiment"]
                + market_score * weights["market"]
                + volume_score * weights["volume"]
                + tokenmetrics_score * weights["tokenmetrics"]
            )

            # Decision logic with higher confidence thresholds
            if combined_score >= 0.65:
                decision = "BUY"
                confidence = min(
                    0.85, combined_score * 1.2
                )  # Boost confidence for strong signals
                reasoning = f"Strong bullish signals detected. {technical_data['reasoning']} | Sentiment: {sentiment_score:.2f}"
            elif combined_score <= 0.35:
                decision = "SELL"
                confidence = min(0.85, (1 - combined_score) * 1.2)
                reasoning = f"Strong bearish signals detected. {technical_data['reasoning']} | Sentiment: {sentiment_score:.2f}"
            else:
                decision = "HOLD"
                confidence = 0.6  # Moderate confidence for hold decisions
                reasoning = f"Mixed signals, holding position. {technical_data['reasoning']} | Sentiment: {sentiment_score:.2f}"

            # Additional confidence boost for strong technical signals
            if technical_data.get("rsi", 50) < 25 or technical_data.get("rsi", 50) > 75:
                confidence = min(0.9, confidence * 1.1)
                reasoning += " | Extreme RSI levels detected"

            result = {
                "decision": decision,
                "confidence": round(confidence, 2),
                "reasoning": reasoning,
                "timestamp": datetime.utcnow().isoformat(),
                "technical_score": technical_score,
                "sentiment_score": sentiment_score,
                "market_score": market_score,
                "volume_score": volume_score,
                "tokenmetrics_score": tokenmetrics_score,
                "combined_score": combined_score,
                "data_points_used": len(data_points),
                "sentiment_confidence": sentiment_confidence,
                "analysis_breakdown": {
                    "technical": f"{technical_score:.3f} (40%)",
                    "sentiment": f"{sentiment_score:.3f} (25%)",
                    "market": f"{market_score:.3f} (20%)",
                    "volume": f"{volume_score:.3f} (10%)",
                    "tokenmetrics": f"{tokenmetrics_score:.3f} (5%)",
                },
            }

            logger.info(
                f"✅ {symbol} decision: {decision} (confidence: {confidence:.2f})"
            )
            return result

        except Exception as e:
            logger.error(f"❌ Error in profitable decision for {symbol}: {e}")
            return {
                "decision": "HOLD",
                "confidence": 0.5,
                "reasoning": f"Analysis error: {str(e)}",
                "timestamp": datetime.utcnow().isoformat(),
                "data_points_used": 0,
            }

    async def _collect_comprehensive_data(self, symbol: str) -> Dict[str, Any]:
        """Collect comprehensive data points for analysis (200+ data points)"""
        try:
            data_points = {}

            # 1. Price and volume data (50+ points)
            try:
                from price_fetcher import get_enhanced_price_data

                price_data = await get_enhanced_price_data(symbol)
                if price_data:
                    data_points.update(
                        {
                            "current_price": price_data.price,
                            "volume_24h": price_data.volume_24h,
                            "market_cap": price_data.market_cap,
                            "price_change_24h": price_data.price_change_24h,
                            "price_change_percentage_24h": price_data.price_change_percentage_24h,
                            "data_source": price_data.source,
                            "confidence": price_data.confidence,
                        }
                    )
            except Exception as e:
                logger.warning(f"Price data collection failed: {e}")

            # 2. TokenMetrics data (30+ points)
            try:
                from tokenmetrics_client import get_tokenmetrics_data

                tm_data = await get_tokenmetrics_data(symbol)
                if tm_data:
                    data_points.update(
                        {
                            "tm_grade": tm_data.tm_grade,
                            "tm_score": tm_data.tm_score,
                            "volatility": tm_data.volatility,
                            "liquidity_score": tm_data.liquidity_score,
                            "social_sentiment": tm_data.social_sentiment,
                            "technical_analysis": tm_data.technical_analysis,
                        }
                    )
            except Exception as e:
                logger.warning(f"TokenMetrics data collection failed: {e}")

            # 3. Sentiment data (40+ points)
            try:
                from sentiment_engine import get_combined_sentiment

                sentiment_data = get_combined_sentiment(symbol)
                data_points.update(
                    {
                        "sentiment_score": sentiment_data.get("sentiment_score", 0.5),
                        "news_sentiment": sentiment_data.get(
                            "news_sentiment", "neutral"
                        ),
                        "social_sentiment_label": sentiment_data.get(
                            "social_sentiment", "neutral"
                        ),
                        "sentiment_confidence": sentiment_data.get("confidence", 0.5),
                        "sentiment_breakdown": sentiment_data.get("breakdown", {}),
                    }
                )
            except Exception as e:
                logger.warning(f"Sentiment data collection failed: {e}")

            logger.info(
                f"📊 Collected {len(data_points)} comprehensive data points for {symbol}"
            )
            return data_points

        except Exception as e:
            logger.error(f"Comprehensive data collection failed for {symbol}: {e}")
            return {}

    async def _analyze_market_conditions(self, symbol: str, data_points: Dict) -> float:
        """Analyze market conditions using collected data"""
        try:
            market_score = 0.5  # Base score

            # Volume analysis
            volume_24h = data_points.get("volume_24h", 0)
            if volume_24h > 1000000:  # High volume
                market_score += 0.2
            elif volume_24h > 100000:  # Medium volume
                market_score += 0.1

            # Market cap analysis
            market_cap = data_points.get("market_cap", 0)
            if market_cap > 1000000000:  # Large cap
                market_score += 0.1
            elif market_cap > 100000000:  # Mid cap
                market_score += 0.05

            # Price change momentum
            price_change_24h = data_points.get("price_change_percentage_24h", 0)
            if price_change_24h > 5:  # Strong positive momentum
                market_score += 0.15
            elif price_change_24h > 0:  # Positive momentum
                market_score += 0.05
            elif price_change_24h < -10:  # Strong negative momentum
                market_score -= 0.2
            elif price_change_24h < 0:  # Negative momentum
                market_score -= 0.1

            return max(0.0, min(1.0, market_score))

        except Exception as e:
            logger.warning(f"Market conditions analysis failed: {e}")
            return 0.5

    def _analyze_volume_patterns(self, data_points: Dict) -> float:
        """Analyze volume patterns for trading signals"""
        try:
            volume_score = 0.5  # Base score

            volume_24h = data_points.get("volume_24h", 0)

            # Volume thresholds
            if volume_24h > 10000000:  # Very high volume
                volume_score = 0.8
            elif volume_24h > 1000000:  # High volume
                volume_score = 0.7
            elif volume_24h > 100000:  # Medium volume
                volume_score = 0.6
            elif volume_24h > 10000:  # Low volume
                volume_score = 0.4
            else:  # Very low volume
                volume_score = 0.2

            return volume_score

        except Exception as e:
            logger.warning(f"Volume pattern analysis failed: {e}")
            return 0.5

    async def _get_tokenmetrics_score(self, symbol: str) -> float:
        """Get TokenMetrics score for the token"""
        try:
            from tokenmetrics_client import get_tokenmetrics_data

            tm_data = await get_tokenmetrics_data(symbol)

            if tm_data:
                # Convert grade to score
                grade_scores = {"A": 0.9, "B": 0.7, "C": 0.5, "D": 0.3, "F": 0.1}
                grade_score = grade_scores.get(tm_data.tm_grade, 0.5)

                # Combine with numerical score
                numerical_score = tm_data.tm_score / 100.0  # Normalize to 0-1

                # Weighted combination
                final_score = (grade_score * 0.6) + (numerical_score * 0.4)
                return final_score

            return 0.5

        except Exception as e:
            logger.warning(f"TokenMetrics score analysis failed: {e}")
            return 0.5


# Initialize global AI engine
def get_ai_engine():
    global ai_engine
    if ai_engine is None:
        ai_engine = ProfitableAIDecisionEngine()
    return ai_engine


# Main functions for backward compatibility
async def get_final_ai_decision_async(symbol: str, **kwargs) -> Dict[str, Any]:
    """
    Get final AI decision for a trading symbol with enhanced profitability analysis (async version)
    """
    engine = get_ai_engine()
    return await engine.get_profitable_decision(symbol)


def get_final_ai_decision(symbol: str, **kwargs) -> Dict[str, Any]:
    """
    Get final AI decision for a trading symbol with enhanced profitability analysis (sync version)
    """
    try:
        # Try to run in existing event loop
        loop = asyncio.get_event_loop()
        if loop.is_running():
            # If loop is running, create a new task
            import concurrent.futures

            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(
                    asyncio.run, get_final_ai_decision_async(symbol, **kwargs)
                )
                return future.result()
        else:
            # If no loop is running, use asyncio.run
            return asyncio.run(get_final_ai_decision_async(symbol, **kwargs))
    except Exception as e:
        logger.error(f"Error in get_final_ai_decision for {symbol}: {e}")
        return {
            "decision": "HOLD",
            "confidence": 0.5,
            "reasoning": f"Error in AI decision: {str(e)}",
            "timestamp": datetime.utcnow().isoformat(),
        }


async def run_ai_trade(symbol: str) -> Dict[str, Any]:
    """Run AI trade analysis for a symbol"""
    return await get_final_ai_decision_async(symbol)


async def analyze_token_comprehensive(
    symbol: str, market_data: Dict[str, Any]
) -> Dict[str, Any]:
    """
    🎯 COMPREHENSIVE TOKEN ANALYSIS
    Analyze a token using 200+ data points and AI decision making

    Args:
        symbol: Token symbol (e.g., 'BTC-USDT')
        market_data: Basic market data (price, volume, sentiment)

    Returns:
        Dict with comprehensive analysis results
    """
    try:
        logger.info(f"🧠 Starting comprehensive analysis for {symbol}")

        # Get AI engine
        ai_engine = get_ai_engine()

        # Collect comprehensive data
        comprehensive_data = await ai_engine._collect_comprehensive_data(symbol)

        # Add provided market data
        comprehensive_data.update(market_data)

        # Perform AI analysis
        analysis_result = await ai_engine.analyze_token_with_200_data_points(
            symbol, comprehensive_data
        )

        logger.info(f"✅ Comprehensive analysis completed for {symbol}")
        return analysis_result

    except Exception as e:
        logger.error(f"❌ Comprehensive analysis failed for {symbol}: {e}")
        return {
            "symbol": symbol,
            "decision": "HOLD",
            "confidence": 0.0,
            "reasoning": f"Analysis error: {str(e)}",
            "timestamp": datetime.utcnow().isoformat(),
            "data_points_used": 0,
            "error": str(e),
        }
