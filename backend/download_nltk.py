import os
import sys
import ssl
import urllib.request


def ensure_nltk_data():
    """Ensure NLTK data is available, download if necessary"""
    # Use current working directory or temp directory instead of hardcoded path
    import tempfile

    default_dir = os.path.join(os.getcwd(), "nltk_data")
    if not os.path.exists(default_dir):
        default_dir = os.path.join(tempfile.gettempdir(), "nltk_data")
    download_dir = os.environ.get("NLTK_DATA", default_dir)

    # Create directory if it doesn't exist
    os.makedirs(download_dir, exist_ok=True)

    # Check if we already have the required data
    required_files = [
        "corpora/vader_lexicon",
        "corpora/wordnet",
        "corpora/omw-1.4",
        "tokenizers/punkt",
        "corpora/stopwords",
    ]

    missing_data = []
    for file_path in required_files:
        full_path = os.path.join(download_dir, file_path)
        if not os.path.exists(full_path):
            missing_data.append(file_path.split("/")[-1])

    if not missing_data:
        print("✅ All NLTK data already available")
        return True

    print(f"📥 Downloading missing NLTK data: {missing_data}")

    try:
        import nltk

        # Set NLTK data path
        nltk.data.path.append(download_dir)

        # Create SSL context that doesn't verify certificates (for Docker environments)
        try:
            _create_unverified_https_context = ssl._create_unverified_context
        except AttributeError:
            pass
        else:
            ssl._create_default_https_context = _create_unverified_https_context

        # Download missing resources with error handling
        resources = ["vader_lexicon", "wordnet", "omw-1.4", "punkt", "stopwords"]

        success_count = 0
        for resource in resources:
            try:
                result = nltk.download(resource, download_dir=download_dir, quiet=False)
                if result:
                    print(f"✅ Downloaded {resource}")
                    success_count += 1
                else:
                    print(f"⚠️  Warning: Could not download {resource}")
            except Exception as e:
                print(f"⚠️  Warning: Could not download {resource}: {e}")

        if success_count > 0:
            print(
                f"✅ NLTK resources download completed ({success_count}/{len(resources)} successful)"
            )
            return True
        else:
            print("⚠️  No NLTK resources were downloaded successfully")
            return False

    except Exception as e:
        print(f"❌ Error downloading NLTK resources: {e}")
        # Try alternative approach
        try:
            print("🔄 Trying alternative download method...")
            import nltk.downloader

            downloader = nltk.downloader.Downloader()

            for resource in [
                "vader_lexicon",
                "wordnet",
                "omw-1.4",
                "punkt",
                "stopwords",
            ]:
                try:
                    downloader.download(resource, download_dir=download_dir)
                    print(f"✅ Downloaded {resource} (alternative method)")
                except Exception as alt_e:
                    print(
                        f"⚠️  Warning: Could not download {resource} (alternative): {alt_e}"
                    )

            return True

        except Exception as alt_e:
            print(f"❌ Alternative download method also failed: {alt_e}")
            return False


def download_nltk_resources():
    """Legacy function for backward compatibility"""
    return ensure_nltk_data()


if __name__ == "__main__":
    ensure_nltk_data()
