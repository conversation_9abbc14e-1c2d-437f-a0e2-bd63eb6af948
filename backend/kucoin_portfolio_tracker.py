#!/usr/bin/env python3
"""
KuCoin Portfolio Tracker
Fetches real portfolio and balance data from KuCoin API
"""

import os
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Tuple
from pathlib import Path

try:
    from kucoin.client import Client
except ImportError:
    try:
        from kucoin import Client
    except ImportError:
        print("KuCoin client not available - install with: pip install kucoin-python")
        Client = None
from config import (
    KUCOIN_API_KEY, 
    KUCOIN_API_SECRET, 
    KUCOIN_API_PASSPHRASE,
    KUCOIN_SANDBOX
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class KuCoinPortfolioTracker:
    """Track and fetch real KuCoin portfolio data"""
    
    def __init__(self):
        """Initialize KuCoin client"""
        try:
            if Client is None:
            print("KuCoin client not available")
            self.client = None
            return
        
        try:
            self.client = Client(
                api_key=KUCOIN_API_KEY,
                api_secret=KUCOIN_API_SECRET,
                passphrase=KUCOIN_API_PASSPHRASE,
                sandbox=KUCOIN_SANDBOX
            )
            logger.info("✅ KuCoin portfolio client initialized successfully")
        except Exception as e:
            logger.error(f"❌ Failed to initialize KuCoin client: {e}")
            self.client = None
    
    def get_account_balances(self) -> Dict[str, Any]:
        """
        Get all account balances from KuCoin
        
        Returns:
            Dictionary with account balances
        """
        if not self.client:
            logger.error("KuCoin client not initialized")
            return {}
        
        try:
            # Get all accounts
            accounts = self.client.get_accounts()
            
            balances = {}
            total_usdt_value = 0.0
            
            if accounts:
                for account in accounts:
                    currency = account.get('currency')
                    balance = float(account.get('balance', 0))
                    available = float(account.get('available', 0))
                    holds = float(account.get('holds', 0))
                    account_type = account.get('type')
                    
                    if balance > 0:  # Only include non-zero balances
                        balances[currency] = {
                            'balance': balance,
                            'available': available,
                            'holds': holds,
                            'account_type': account_type,
                            'currency': currency
                        }
                        
                        # Calculate USDT value (simplified - you might want to get real prices)
                        if currency == 'USDT':
                            total_usdt_value += balance
                        elif currency == 'BTC':
                            # Rough BTC to USDT conversion (you should get real price)
                            total_usdt_value += balance * 95000  # Approximate BTC price
                        elif currency == 'ETH':
                            # Rough ETH to USDT conversion
                            total_usdt_value += balance * 2500  # Approximate ETH price
            
            logger.info(f"✅ Fetched balances for {len(balances)} currencies")
            return {
                'balances': balances,
                'total_usdt_value': total_usdt_value,
                'last_updated': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to fetch account balances: {e}")
            return {}
    
    def get_trading_account_balance(self) -> float:
        """
        Get main trading account balance in USDT
        
        Returns:
            Total balance in USDT
        """
        if not self.client:
            logger.error("KuCoin client not initialized")
            return 1000.0  # Default fallback
        
        try:
            # Get trading accounts only
            accounts = self.client.get_accounts(account_type='trade')
            
            total_balance = 0.0
            
            if accounts:
                for account in accounts:
                    currency = account.get('currency')
                    balance = float(account.get('balance', 0))
                    
                    if balance > 0:
                        if currency == 'USDT':
                            total_balance += balance
                        elif currency == 'BTC':
                            # Convert BTC to USDT (you should get real price)
                            total_balance += balance * 95000
                        elif currency == 'ETH':
                            # Convert ETH to USDT
                            total_balance += balance * 2500
                        # Add more currency conversions as needed
            
            logger.info(f"✅ Trading account balance: ${total_balance:.2f} USDT")
            return total_balance if total_balance > 0 else 1000.0
            
        except Exception as e:
            logger.error(f"❌ Failed to fetch trading balance: {e}")
            return 1000.0  # Default fallback
    
    def get_portfolio_positions(self) -> Dict[str, Any]:
        """
        Get current portfolio positions
        
        Returns:
            Dictionary with position data
        """
        if not self.client:
            logger.error("KuCoin client not initialized")
            return {}
        
        try:
            accounts = self.client.get_accounts(account_type='trade')
            positions = {}
            
            if accounts:
                for account in accounts:
                    currency = account.get('currency')
                    balance = float(account.get('balance', 0))
                    available = float(account.get('available', 0))
                    holds = float(account.get('holds', 0))
                    
                    if balance > 0 and currency != 'USDT':  # Exclude USDT (base currency)
                        positions[currency] = {
                            'amount': balance,
                            'available': available,
                            'holds': holds,
                            'currency': currency,
                            'last_updated': datetime.now().isoformat()
                        }
            
            logger.info(f"✅ Found {len(positions)} active positions")
            return positions
            
        except Exception as e:
            logger.error(f"❌ Failed to fetch portfolio positions: {e}")
            return {}
    
    def get_portfolio_summary(self) -> Tuple[Dict[str, Any], float]:
        """
        Get complete portfolio summary
        
        Returns:
            Tuple of (positions_dict, total_balance)
        """
        logger.info("🔍 Fetching portfolio summary from KuCoin...")
        
        try:
            # Get positions and balance
            positions = self.get_portfolio_positions()
            balance = self.get_trading_account_balance()
            
            # Format for compatibility with existing code
            portfolio_data = {
                'positions': positions,
                'balance': balance,
                'last_updated': datetime.now().isoformat(),
                'source': 'kucoin_api'
            }
            
            logger.info(f"✅ Portfolio: {len(positions)} positions, ${balance:.2f} balance")
            return positions, balance
            
        except Exception as e:
            logger.error(f"❌ Failed to get portfolio summary: {e}")
            return {}, 1000.0  # Default fallback
    
    def save_portfolio_snapshot(self) -> bool:
        """
        Save current portfolio snapshot to local file
        
        Returns:
            True if successful, False otherwise
        """
        try:
            positions, balance = self.get_portfolio_summary()
            
            portfolio_data = {
                'portfolio': positions,
                'balance': balance,
                'last_updated': datetime.now().isoformat(),
                'source': 'kucoin_api'
            }
            
            # Save to local file
            portfolio_file = Path(__file__).resolve().parent / "data" / "portfolio.json"
            portfolio_file.parent.mkdir(exist_ok=True)
            
            with open(portfolio_file, 'w') as f:
                json.dump(portfolio_data, f, indent=2)
            
            logger.info(f"✅ Portfolio snapshot saved to {portfolio_file}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to save portfolio snapshot: {e}")
            return False

# Global instance
kucoin_portfolio = KuCoinPortfolioTracker()

def get_kucoin_portfolio() -> Tuple[Dict[str, Any], float]:
    """
    Main function to get portfolio from KuCoin
    
    Returns:
        Tuple of (positions_dict, balance)
    """
    return kucoin_portfolio.get_portfolio_summary()

def get_kucoin_balance() -> float:
    """
    Get just the balance from KuCoin
    
    Returns:
        Balance in USDT
    """
    return kucoin_portfolio.get_trading_account_balance()

if __name__ == "__main__":
    # Test the portfolio tracker
    print("🧪 Testing KuCoin Portfolio Tracker")
    print("=" * 50)
    
    tracker = KuCoinPortfolioTracker()
    
    # Test balance
    balance = tracker.get_trading_account_balance()
    print(f"Trading Balance: ${balance:.2f}")
    
    # Test positions
    positions = tracker.get_portfolio_positions()
    print(f"Active Positions: {len(positions)}")
    
    # Test full summary
    pos, bal = tracker.get_portfolio_summary()
    print(f"Portfolio Summary: {len(pos)} positions, ${bal:.2f} balance")
    
    # Save snapshot
    saved = tracker.save_portfolio_snapshot()
    print(f"Snapshot Saved: {saved}")
