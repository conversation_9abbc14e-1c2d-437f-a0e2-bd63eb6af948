import os
import logging
import traceback
import json
import csv
import threading
from datetime import datetime
from collections import defaultdict
from typing import Dict, Any, Tuple, List, Optional, Union
from pathlib import Path

# Enhanced imports with fallback for cloud storage
try:
    from google.cloud import firestore
    from config import FIRESTORE_CREDENTIALS_PATH

    FIRESTORE_AVAILABLE = True
except ImportError:
    FIRESTORE_AVAILABLE = False
    logger = logging.getLogger(__name__)
    logger.warning("Firestore not available, using local storage only")

logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)

# Enhanced data storage configuration
DATA_DIR = "backend/data"
TRADES_FILE = os.path.join(DATA_DIR, "trades.csv")
LIVE_TRADES_FILE = os.path.join(DATA_DIR, "live_trades.json")
PORTFOLIO_FILE = os.path.join(DATA_DIR, "portfolio.json")
TRADE_STORAGE_FILE = os.path.join(DATA_DIR, "trade_storage.jsonl")
PNL_REPORT_FILE = os.path.join(DATA_DIR, "pnl_report.json")

# Thread safety for concurrent access
_storage_lock = threading.RLock()

# Initialize Firestore client (optional)
db: Optional[Any] = None
if FIRESTORE_AVAILABLE:
    try:
        from config import FIRESTORE_CREDENTIALS_PATH
    except ImportError:
        FIRESTORE_CREDENTIALS_PATH = None


def _initialize_firestore_client():
    """Initialize Firestore client if available and configured."""
    global db
    if not FIRESTORE_AVAILABLE:
        return False

    if db is None:
        try:
            # Check if credentials path is available and valid
            credentials_path = globals().get("FIRESTORE_CREDENTIALS_PATH")

            if credentials_path and os.path.exists(credentials_path):
                from google.cloud import firestore

                db = firestore.Client.from_service_account_json(credentials_path)
                logger.info(
                    "✅ Firestore client initialized using service account key."
                )
            else:
                # Fallback to default credentials (e.g., when running on GCP services)
                from google.cloud import firestore

                db = firestore.Client()
                logger.info(
                    "✅ Firestore client initialized using default credentials."
                )
        except Exception as e:
            logger.error(
                f"❌ Failed to initialize Firestore client: {e}", exc_info=True
            )
            db = None  # Ensure db is None on failure
    return db is not None


# Consistent collection/document names
TRADES_COLLECTION = "trades"
PORTFOLIO_DOCUMENT = "portfolio/state"


# Enhanced local storage functions
def _ensure_data_directory():
    """Ensure data directory exists."""
    os.makedirs(DATA_DIR, exist_ok=True)


def _save_json_file(filepath: str, data: Any) -> bool:
    """Thread-safe JSON file saving."""
    try:
        with _storage_lock:
            _ensure_data_directory()
            with open(filepath, "w") as f:
                json.dump(data, f, indent=2, default=str)
        return True
    except Exception as e:
        logger.error(f"Failed to save {filepath}: {e}")
        return False


def _load_json_file(filepath: str, default: Any = None) -> Any:
    """Thread-safe JSON file loading."""
    try:
        with _storage_lock:
            if os.path.exists(filepath):
                with open(filepath, "r") as f:
                    return json.load(f)
            else:
                return default if default is not None else {}
    except Exception as e:
        logger.error(f"Failed to load {filepath}: {e}")
        return default if default is not None else {}


def _append_to_jsonl(filepath: str, data: Dict[str, Any]) -> bool:
    """Append data to JSONL file for trade history."""
    try:
        with _storage_lock:
            _ensure_data_directory()
            with open(filepath, "a") as f:
                f.write(json.dumps(data, default=str) + "\n")
        return True
    except Exception as e:
        logger.error(f"Failed to append to {filepath}: {e}")
        return False


def _save_to_csv(filepath: str, data: Dict[str, Any], headers: List[str]) -> bool:
    """Save trade data to CSV file."""
    try:
        with _storage_lock:
            _ensure_data_directory()
            file_exists = os.path.exists(filepath)

            with open(filepath, "a", newline="") as f:
                writer = csv.DictWriter(f, fieldnames=headers)
                if not file_exists:
                    writer.writeheader()
                writer.writerow(data)
        return True
    except Exception as e:
        logger.error(f"Failed to save to CSV {filepath}: {e}")
        return False


def log_trade(
    token: str,
    side: str,
    amount: float,
    price: float,
    strategy: str = "AI",
    reason: str = "",
    signal_score=None,
    news_source=None,
):
    """
    Enhanced trade logging to both local storage and Firestore (if available).
    """
    timestamp = datetime.now()

    # Prepare trade data
    trade_data = {
        "timestamp": timestamp.isoformat(),
        "token": token,
        "side": side.upper(),
        "amount": float(amount),
        "price": float(price),
        "value_usd": float(amount * price),
        "strategy": strategy,
        "reason": reason,
        "signal_score": signal_score,
        "news_source": news_source,
        "trade_id": f"{token}_{side}_{timestamp.strftime('%Y%m%d_%H%M%S')}",
    }

    # Save to local storage (primary)
    success_local = _save_trade_locally(trade_data)

    # Save to Firestore (secondary, if available)
    success_firestore = _save_trade_to_firestore(trade_data)

    if success_local:
        logger.info(f"✅ Trade logged locally: {side} {amount} {token} @ ${price:.4f}")
    else:
        logger.error(f"❌ Failed to log trade locally: {side} {token}")

    if success_firestore:
        logger.debug(f"✅ Trade also logged to Firestore: {trade_data['trade_id']}")

    return success_local


def _save_trade_locally(trade_data: Dict[str, Any]) -> bool:
    """Save trade to local storage files."""
    try:
        # 1. Save to CSV for easy analysis
        csv_headers = [
            "timestamp",
            "token",
            "side",
            "amount",
            "price",
            "value_usd",
            "strategy",
            "reason",
            "signal_score",
            "news_source",
            "trade_id",
        ]
        _save_to_csv(TRADES_FILE, trade_data, csv_headers)

        # 2. Append to JSONL for detailed history
        _append_to_jsonl(TRADE_STORAGE_FILE, trade_data)

        # 3. Update live trades list
        _update_live_trades(trade_data)

        return True
    except Exception as e:
        logger.error(f"Failed to save trade locally: {e}")
        return False


def _save_trade_to_firestore(trade_data: Dict[str, Any]) -> bool:
    """Save trade to Firestore if available."""
    if not FIRESTORE_AVAILABLE or not _initialize_firestore_client():
        return False

    try:
        global db
        if db is None:
            return False

        # Save to Firestore
        doc_ref = db.collection(TRADES_COLLECTION).document(trade_data["trade_id"])
        doc_ref.set(trade_data)
        return True
    except Exception as e:
        logger.error(f"Failed to save trade to Firestore: {e}")
        return False


def _update_live_trades(trade_data: Dict[str, Any]) -> bool:
    """Update live trades list with recent trades."""
    try:
        # Load existing live trades
        live_trades = _load_json_file(LIVE_TRADES_FILE, {"trades": []})

        # Ensure trades list exists
        if "trades" not in live_trades:
            live_trades["trades"] = []

        # Add new trade to the beginning
        live_trades["trades"].insert(0, trade_data)

        # Keep only last 100 trades for performance
        live_trades["trades"] = live_trades["trades"][:100]

        # Update metadata
        live_trades["last_updated"] = datetime.now().isoformat()
        live_trades["total_trades"] = len(live_trades["trades"])

        # Save back to file
        return _save_json_file(LIVE_TRADES_FILE, live_trades)
    except Exception as e:
        logger.error(f"Failed to update live trades: {e}")
        return False


def save_portfolio_json(portfolio: dict, balance: float):
    """
    Enhanced portfolio saving to both local storage and Firestore.
    """
    timestamp = datetime.now()

    # Prepare portfolio data with enhanced metadata
    portfolio_data = {
        "portfolio": portfolio,
        "balance": float(balance),
        "last_updated": timestamp.isoformat(),
        "total_positions": len(portfolio),
        "total_value": sum(
            pos.get("qty", 0) * pos.get("current_price", pos.get("avg_price", 0))
            for pos in portfolio.values()
        ),
    }

    # Save to local storage (primary)
    success_local = _save_json_file(PORTFOLIO_FILE, portfolio_data)

    # Save to Firestore (secondary, if available)
    success_firestore = _save_portfolio_to_firestore(portfolio_data)

    if success_local:
        logger.info(
            f"✅ Portfolio saved locally: {len(portfolio)} positions, ${balance:.2f} balance"
        )
    else:
        logger.error("❌ Failed to save portfolio locally")

    return success_local


def _save_portfolio_to_firestore(portfolio_data: Dict[str, Any]) -> bool:
    """Save portfolio to Firestore if available."""
    if not FIRESTORE_AVAILABLE or not _initialize_firestore_client():
        return False

    try:
        global db
        if db is None:
            return False

        # Convert timestamp for Firestore
        firestore_data = portfolio_data.copy()
        if FIRESTORE_AVAILABLE:
            try:
                from google.cloud import firestore

                firestore_data["last_updated"] = firestore.SERVER_TIMESTAMP
            except ImportError:
                pass

        # Save to Firestore
        doc_ref = db.document(PORTFOLIO_DOCUMENT)
        doc_ref.set(firestore_data)
        return True
    except Exception as e:
        logger.error(f"Failed to save portfolio to Firestore: {e}")
        return False


def load_portfolio_json() -> Tuple[dict, float]:
    """
    Enhanced portfolio loading from local storage with Firestore fallback.
    Returns empty portfolio and 1000.0 balance if not found.
    """
    # Try loading from local storage first
    portfolio_data = _load_json_file(PORTFOLIO_FILE, None)

    if portfolio_data:
        portfolio = portfolio_data.get("portfolio", {})
        balance = portfolio_data.get("balance", 1000.0)
        logger.info(
            f"✅ Portfolio loaded locally: {len(portfolio)} positions, ${balance:.2f} balance"
        )
        return portfolio, balance

    # Fallback to Firestore if local file not found
    if FIRESTORE_AVAILABLE and _initialize_firestore_client():
        try:
            global db
            if db is not None:
                doc_ref = db.document(PORTFOLIO_DOCUMENT)
                doc = doc_ref.get()
                if doc.exists:
                    data = doc.to_dict()
                    if data:
                        portfolio = data.get("portfolio", {})
                        balance = data.get("balance", 1000.0)
                        logger.info("✅ Portfolio loaded from Firestore fallback")

                        # Save to local storage for future use
                        _save_json_file(PORTFOLIO_FILE, data)
                        return portfolio, balance
        except Exception as e:
            logger.error(f"Failed to load portfolio from Firestore: {e}")

    # Return defaults if nothing found
    logger.warning("Portfolio not found, returning defaults")
    return {}, 1000.0


def load_live_trades() -> Dict[str, Any]:
    """
    Enhanced live trades loading from CSV trades file with real trading data.
    """
    try:
        # Load real trades from CSV file
        trades_csv_path = Path(__file__).resolve().parent / "data" / "trades.csv"

        if trades_csv_path.exists():
            import pandas as pd

            # Read CSV file
            df = pd.read_csv(trades_csv_path)

            if not df.empty:
                # Convert to list of dictionaries and get latest 50 trades
                trades_list = df.tail(50).to_dict("records")

                # Format trades for frontend
                formatted_trades = []
                for trade in trades_list:
                    formatted_trade = {
                        "time": trade.get("timestamp", ""),
                        "token": trade.get("token", ""),
                        "type": trade.get("side", "").lower(),
                        "quantity": float(trade.get("amount", 0)),
                        "price": float(trade.get("price", 0)),
                        "value": float(trade.get("value", 0)),
                        "profit": 0.0,  # Will be calculated based on current price vs buy price
                    }
                    formatted_trades.append(formatted_trade)

                # Reverse to show newest first
                formatted_trades.reverse()

                live_trades_data = {
                    "trades": formatted_trades,
                    "last_updated": datetime.now().isoformat(),
                    "total_trades": len(formatted_trades),
                    "source": "real_trades_csv",
                }

                logger.info(f"✅ Loaded {len(formatted_trades)} real trades from CSV")
                return live_trades_data

    except Exception as e:
        logger.error(f"Error loading real trades from CSV: {e}")

    # Fallback to existing JSON file if CSV fails
    live_trades = _load_json_file(LIVE_TRADES_FILE, None)

    if live_trades and "trades" in live_trades:
        logger.info(
            f"✅ Loaded {len(live_trades['trades'])} live trades from JSON fallback"
        )
        return live_trades

    # Fallback to Firestore if local file not found
    if FIRESTORE_AVAILABLE:
        try:
            if _initialize_firestore_client():
                global db
                if db is not None:
                    # Import firestore for Query.DESCENDING
                    from google.cloud import firestore

                    # Fetch latest 100 trades from Firestore
                    trades_ref = (
                        db.collection(TRADES_COLLECTION)
                        .order_by("timestamp", direction=firestore.Query.DESCENDING)
                        .limit(100)
                    )

                    trades_list = []
                    for trade_doc in trades_ref.stream():
                        trade_data = trade_doc.to_dict()
                        if trade_data:
                            # Convert Firestore timestamp to ISO format
                            if hasattr(trade_data.get("timestamp"), "isoformat"):
                                trade_data["timestamp"] = trade_data[
                                    "timestamp"
                                ].isoformat()
                            trades_list.append(trade_data)

                    if trades_list:
                        live_trades_data = {
                            "trades": trades_list,
                            "last_updated": datetime.now().isoformat(),
                            "total_trades": len(trades_list),
                            "source": "firestore_fallback",
                        }

                        # Save to local storage for future use
                        _save_json_file(LIVE_TRADES_FILE, live_trades_data)

                        logger.info(
                            f"✅ Loaded {len(trades_list)} trades from Firestore fallback"
                        )
                        return live_trades_data
        except Exception as e:
            logger.error(f"Failed to load live trades from Firestore: {e}")

    # Return empty if nothing found
    logger.warning("No live trades found, returning empty")
    return {"trades": [], "last_updated": datetime.now().isoformat(), "total_trades": 0}


def update_portfolio_state(token: str, side: str, amount: float, price: float):
    """
    Update portfolio state in Firestore after a paper trade.
    Adjusts quantities and average prices accordingly.
    """
    if not _initialize_firestore_client():
        logger.error("Skipping portfolio update: Firestore client not initialized.")
        return

    try:
        portfolio, balance = load_portfolio_json()  # Load current state from Firestore
        token = token.upper()

        if token not in portfolio:
            portfolio[token] = {"qty": 0.0, "avg_price": 0.0}

        if side.upper() == "BUY":
            cost = amount * price
            total_qty = portfolio[token]["qty"] + amount
            if total_qty > 0:
                new_avg = (
                    (portfolio[token]["qty"] * portfolio[token]["avg_price"]) + cost
                ) / total_qty
            else:
                new_avg = price
            portfolio[token]["qty"] = total_qty
            portfolio[token]["avg_price"] = new_avg
            balance -= cost

        elif side.upper() == "SELL":
            proceeds = amount * price
            portfolio[token]["qty"] = max(0.0, portfolio[token]["qty"] - amount)
            if portfolio[token]["qty"] == 0:
                portfolio[token]["avg_price"] = 0.0
            balance += proceeds

        else:
            logger.warning(f"Unknown side '{side}' in portfolio update.")

        save_portfolio_json(portfolio, balance)  # Save updated state to Firestore
        logger.info(
            f"Portfolio updated for {side} {amount:.6f} {token} at ${price:.4f}"
        )

    except Exception as e:
        logger.error(
            f"Failed to update portfolio state in Firestore: {e}", exc_info=True
        )


def print_pnl_dashboard():
    """
    Print profit and loss dashboard summary from Firestore.
    Aggregates investment, realized value, and calculates P/L % per token.
    """
    if not _initialize_firestore_client():
        logger.error("Skipping PnL dashboard print: Firestore client not initialized.")
        return

    token_stats = defaultdict(
        lambda: {"invested": 0.0, "realized": 0.0, "buy_qty": 0.0, "sell_qty": 0.0}
    )

    try:
        if db is None:
            logger.error("Firestore client is None. Skipping trade summary load.")
            return
        trades_ref = db.collection(TRADES_COLLECTION)
        trades = trades_ref.stream()

        for trade_doc in trades:
            trade = trade_doc.to_dict()
            if trade is None:
                continue
            token = trade.get("token", "").upper()
            side = trade.get("side", "").upper()
            value = float(trade.get("value", 0))
            qty = float(trade.get("amount", 0))

            if side == "BUY":
                token_stats[token]["invested"] += value
                token_stats[token]["buy_qty"] += qty
            elif side == "SELL":
                token_stats[token]["realized"] += value
                token_stats[token]["sell_qty"] += qty

        logger.info("\n💹 P&L DASHBOARD")
        logger.info("------------------------------------------------------------")
        logger.info(f"{'Token':<15}{'Invested':<12}{'Realized':<12}{'P/L %'}")
        logger.info("------------------------------------------------------------")

        for token, stats in token_stats.items():
            invested = stats["invested"]
            realized = stats["realized"]
            pnl_pct = ((realized - invested) / invested) * 100 if invested > 0 else 0.0
            logger.info(f"{token:<15}{invested:<12.2f}{realized:<12.2f}{pnl_pct:.2f}%")

        logger.info("------------------------------------------------------------\n")

    except Exception as e:
        logger.error(
            f"Failed to print PnL dashboard from Firestore: {e}", exc_info=True
        )


def get_pnl_summary() -> dict:
    """
    Get profit and loss summary used in Telegram notifications from Firestore.
    Returns dict with total buys, sells, profit, loss, and net PnL.
    """
    if not _initialize_firestore_client():
        logger.error("Skipping PnL summary: Firestore client not initialized.")
        return {
            "total_buys": 0,
            "total_sells": 0,
            "profit": 0.0,
            "loss": 0.0,
            "net_pnl": 0.0,
        }

    global db
    if db is None:
        logger.error(
            "Firestore client is None after initialization attempt. Skipping PnL summary."
        )
        return {
            "total_buys": 0,
            "total_sells": 0,
            "profit": 0.0,
            "loss": 0.0,
            "net_pnl": 0.0,
        }

    stats = defaultdict(float)
    try:
        if db is None:
            logger.error("Firestore client is None. Skipping PnL summary.")
            return {
                "total_buys": 0,
                "total_sells": 0,
                "profit": 0.0,
                "loss": 0.0,
                "net_pnl": 0.0,
            }
        trades_ref = db.collection(TRADES_COLLECTION) if db is not None else None
        if trades_ref is None:
            logger.error("Firestore client is None. Cannot access collection.")
            return {
                "total_buys": 0,
                "total_sells": 0,
                "profit": 0.0,
                "loss": 0.0,
                "net_pnl": 0.0,
            }
        trades = trades_ref.stream()

        for trade_doc in trades:
            trade = trade_doc.to_dict()
            if trade is None:
                continue
            side = trade.get("side", "").upper()
            value = float(trade.get("value", 0))
            if side == "BUY":
                stats["total_buys"] += 1
                stats["loss"] += value
            elif side == "SELL":
                stats["total_sells"] += 1
                stats["profit"] += value
        stats["net_pnl"] = stats["profit"] - stats["loss"]
    except Exception as e:
        logger.error(f"Failed to get PnL summary from Firestore: {e}", exc_info=True)
    return stats


def load_trade_summary() -> dict:
    """
    Load trade summary for API usage from Firestore.
    Counts total trades, total profit, and profits per token.
    """
    summary = {"total_trades": 0, "total_profit": 0.0, "tokens": {}}
    if not _initialize_firestore_client():
        logger.error("Skipping trade summary load: Firestore client not initialized.")
        return summary

    try:
        if db is None:
            logger.error("Firestore client is None. Skipping trade summary load.")
            return summary
        trades_ref = db.collection(TRADES_COLLECTION)
        trades = trades_ref.stream()

        for trade_doc in trades:
            trade = trade_doc.to_dict()
            if trade is None:
                continue
            token = trade.get("token", "").upper()
            side = trade.get("side", "").upper()
            if not token or not side:
                continue
            summary["total_trades"] += 1
            value = float(trade.get("value", 0))
            if side == "SELL":
                summary["total_profit"] += value
            summary["tokens"].setdefault(token, 0.0)
            if side == "SELL":
                summary["tokens"][token] += value
        logger.info(f"Loaded {summary['total_trades']} trades from Firestore.")
    except Exception as e:
        logger.error(f"Error loading trade summary from Firestore: {e}", exc_info=True)
    return summary
