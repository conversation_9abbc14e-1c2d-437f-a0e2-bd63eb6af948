#!/usr/bin/env python3
"""
TokenMetrics AI Signals Fetcher
Fetches real AI signals and moonshots data from TokenMetrics API
"""

import os
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path

from tokenmetrics_client import TokenMetricsClient
from config import TOKENMETRICS_API_KEY

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TokenMetricsAISignals:
    """Fetch AI signals and moonshots from TokenMetrics"""
    
    def __init__(self):
        """Initialize TokenMetrics client"""
        try:
            self.client = TokenMetricsClient()
            logger.info("✅ TokenMetrics AI signals client initialized")
        except Exception as e:
            logger.error(f"❌ Failed to initialize TokenMetrics client: {e}")
            self.client = None
    
    def get_moonshots_data(self, limit: int = 50) -> List[Dict[str, Any]]:
        """
        Get moonshots data from TokenMetrics
        
        Args:
            limit: Number of moonshots to fetch
            
        Returns:
            List of moonshot dictionaries
        """
        if not self.client:
            logger.error("TokenMetrics client not initialized")
            return []
        
        try:
            # Get top tokens first
            from kucoin_api import get_kucoin_tokens
            kucoin_tokens = get_kucoin_tokens(limit=100)
            
            moonshots = []
            processed_count = 0
            
            for token_data in kucoin_tokens[:limit]:
                try:
                    symbol = token_data.get('symbol', '').replace('-USDT', '')
                    if not symbol:
                        continue
                    
                    # Get TokenMetrics data for this token
                    tm_data = self.client.get_token_analysis(symbol)
                    
                    if tm_data and tm_data.get('available', False):
                        moonshot = {
                            'symbol': symbol,
                            'name': token_data.get('name', symbol),
                            'price': token_data.get('price', 0),
                            'change_24h': token_data.get('changeRate', 0),
                            'volume_24h': token_data.get('vol', 0),
                            'market_cap': token_data.get('market_cap', 0),
                            'signal': tm_data.get('signal', 'NEUTRAL'),
                            'confidence': tm_data.get('confidence', 0.5),
                            'ai_score': tm_data.get('price_analysis', {}).get('ai_score', 0.5),
                            'sentiment': tm_data.get('price_analysis', {}).get('sentiment', 0.5),
                            'technical_score': tm_data.get('price_analysis', {}).get('technical_score', 0.5),
                            'fundamental_score': tm_data.get('price_analysis', {}).get('fundamental_score', 0.5),
                            'risk_level': self._calculate_risk_level(tm_data),
                            'prediction': self._generate_prediction(tm_data),
                            'last_updated': datetime.now().isoformat(),
                            'source': 'tokenmetrics_api'
                        }
                        moonshots.append(moonshot)
                        processed_count += 1
                        
                        if len(moonshots) >= limit:
                            break
                            
                except Exception as e:
                    logger.debug(f"Error processing token {symbol}: {e}")
                    continue
            
            # Sort by AI score (highest first)
            moonshots.sort(key=lambda x: x['ai_score'], reverse=True)
            
            logger.info(f"✅ Fetched {len(moonshots)} moonshots from {processed_count} tokens")
            return moonshots
            
        except Exception as e:
            logger.error(f"❌ Failed to fetch moonshots data: {e}")
            return []
    
    def get_ai_signals(self, symbols: List[str] = None) -> List[Dict[str, Any]]:
        """
        Get AI trading signals for specific symbols
        
        Args:
            symbols: List of symbols to get signals for
            
        Returns:
            List of AI signal dictionaries
        """
        if not self.client:
            logger.error("TokenMetrics client not initialized")
            return []
        
        if not symbols:
            # Get top KuCoin tokens if no symbols provided
            from kucoin_api import get_kucoin_tokens
            kucoin_tokens = get_kucoin_tokens(limit=50)
            symbols = [token.get('symbol', '').replace('-USDT', '') for token in kucoin_tokens]
            symbols = [s for s in symbols if s]  # Remove empty strings
        
        signals = []
        
        for symbol in symbols[:50]:  # Limit to avoid rate limits
            try:
                tm_data = self.client.get_token_analysis(symbol)
                
                if tm_data and tm_data.get('available', False):
                    signal = {
                        'symbol': symbol,
                        'signal': tm_data.get('signal', 'NEUTRAL'),
                        'confidence': tm_data.get('confidence', 0.5),
                        'ai_score': tm_data.get('price_analysis', {}).get('ai_score', 0.5),
                        'sentiment': tm_data.get('price_analysis', {}).get('sentiment', 0.5),
                        'technical_score': tm_data.get('price_analysis', {}).get('technical_score', 0.5),
                        'fundamental_score': tm_data.get('price_analysis', {}).get('fundamental_score', 0.5),
                        'recommendation': self._generate_recommendation(tm_data),
                        'risk_assessment': self._calculate_risk_level(tm_data),
                        'timestamp': datetime.now().isoformat(),
                        'source': 'tokenmetrics_ai'
                    }
                    signals.append(signal)
                    
            except Exception as e:
                logger.debug(f"Error getting signal for {symbol}: {e}")
                continue
        
        # Sort by confidence (highest first)
        signals.sort(key=lambda x: x['confidence'], reverse=True)
        
        logger.info(f"✅ Generated {len(signals)} AI signals")
        return signals
    
    def _calculate_risk_level(self, tm_data: Dict[str, Any]) -> str:
        """Calculate risk level based on TokenMetrics data"""
        try:
            confidence = tm_data.get('confidence', 0.5)
            ai_score = tm_data.get('price_analysis', {}).get('ai_score', 0.5)
            
            if confidence > 0.8 and ai_score > 0.7:
                return 'LOW'
            elif confidence > 0.6 and ai_score > 0.5:
                return 'MEDIUM'
            else:
                return 'HIGH'
        except:
            return 'MEDIUM'
    
    def _generate_prediction(self, tm_data: Dict[str, Any]) -> str:
        """Generate price prediction based on TokenMetrics data"""
        try:
            signal = tm_data.get('signal', 'NEUTRAL')
            confidence = tm_data.get('confidence', 0.5)
            
            if signal == 'BUY' and confidence > 0.7:
                return 'Strong Upward Momentum Expected'
            elif signal == 'BUY':
                return 'Moderate Upward Potential'
            elif signal == 'SELL' and confidence > 0.7:
                return 'Strong Downward Pressure Expected'
            elif signal == 'SELL':
                return 'Moderate Downward Risk'
            else:
                return 'Sideways Movement Expected'
        except:
            return 'Uncertain Market Conditions'
    
    def _generate_recommendation(self, tm_data: Dict[str, Any]) -> str:
        """Generate trading recommendation"""
        try:
            signal = tm_data.get('signal', 'NEUTRAL')
            confidence = tm_data.get('confidence', 0.5)
            
            if signal == 'BUY' and confidence > 0.8:
                return 'STRONG BUY'
            elif signal == 'BUY' and confidence > 0.6:
                return 'BUY'
            elif signal == 'SELL' and confidence > 0.8:
                return 'STRONG SELL'
            elif signal == 'SELL' and confidence > 0.6:
                return 'SELL'
            else:
                return 'HOLD'
        except:
            return 'HOLD'
    
    def get_comprehensive_ai_data(self) -> Dict[str, Any]:
        """
        Get comprehensive AI data including moonshots and signals
        
        Returns:
            Dictionary with all AI data
        """
        logger.info("🔍 Fetching comprehensive AI data from TokenMetrics...")
        
        try:
            # Get moonshots
            moonshots = self.get_moonshots_data(limit=30)
            
            # Get AI signals for top tokens
            top_symbols = [m['symbol'] for m in moonshots[:20]]
            ai_signals = self.get_ai_signals(symbols=top_symbols)
            
            result = {
                'moonshots': moonshots,
                'ai_signals': ai_signals,
                'summary': {
                    'total_moonshots': len(moonshots),
                    'total_signals': len(ai_signals),
                    'buy_signals': len([s for s in ai_signals if s['signal'] == 'BUY']),
                    'sell_signals': len([s for s in ai_signals if s['signal'] == 'SELL']),
                    'hold_signals': len([s for s in ai_signals if s['signal'] == 'NEUTRAL']),
                    'avg_confidence': sum(s['confidence'] for s in ai_signals) / len(ai_signals) if ai_signals else 0,
                    'last_updated': datetime.now().isoformat()
                },
                'source': 'tokenmetrics_comprehensive'
            }
            
            logger.info(f"✅ Comprehensive AI data: {len(moonshots)} moonshots, {len(ai_signals)} signals")
            return result
            
        except Exception as e:
            logger.error(f"❌ Failed to get comprehensive AI data: {e}")
            return {
                'moonshots': [],
                'ai_signals': [],
                'summary': {'error': str(e)},
                'source': 'error'
            }

# Global instance
tm_ai_signals = TokenMetricsAISignals()

def get_tokenmetrics_moonshots(limit: int = 50) -> List[Dict[str, Any]]:
    """
    Main function to get moonshots from TokenMetrics
    
    Args:
        limit: Number of moonshots to fetch
        
    Returns:
        List of moonshot dictionaries
    """
    return tm_ai_signals.get_moonshots_data(limit)

def get_tokenmetrics_ai_signals(symbols: List[str] = None) -> List[Dict[str, Any]]:
    """
    Main function to get AI signals from TokenMetrics
    
    Args:
        symbols: List of symbols to analyze
        
    Returns:
        List of AI signal dictionaries
    """
    return tm_ai_signals.get_ai_signals(symbols)

if __name__ == "__main__":
    # Test the AI signals fetcher
    print("🧪 Testing TokenMetrics AI Signals")
    print("=" * 50)
    
    ai_signals = TokenMetricsAISignals()
    
    # Test moonshots
    moonshots = ai_signals.get_moonshots_data(limit=10)
    print(f"Moonshots: {len(moonshots)} found")
    
    # Test AI signals
    signals = ai_signals.get_ai_signals(['BTC', 'ETH', 'BNB'])
    print(f"AI Signals: {len(signals)} generated")
    
    # Test comprehensive data
    comprehensive = ai_signals.get_comprehensive_ai_data()
    print(f"Comprehensive: {comprehensive['summary']}")
